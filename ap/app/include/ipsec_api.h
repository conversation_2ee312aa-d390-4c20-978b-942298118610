#ifndef __VOLTE_PROXY_H__
#define __VOLTE_PROXY_H__
#include "softap_api.h"

#define IPSEC_PROTO_BASE	0x00
#define	IPSEC_PROTO_UNKOWN	IPSEC_PROTO_BASE
#define IPSEC_PROTO_UDP		0x01
#define	IPSEC_PROTO_TCP		0x02
#define IPSEC_PROTO_TCP_UDP	0x03
#define IPSEC_PROTO_MAX		IPSEC_PROTO_TCP_UDP
#define IPSEC_NETIF_NAME_LEN	20
#define IPSEC_NV_NAME_LEN		30
#define IPSEC_IPV4ADDLEN_MAX         16
#define IPSEC_IPV6ADDLEN_MAX         46


typedef struct ipsec_set_msg 
{
	char dir[8];    /*in or out*/
	int  IsIpv4;
	unsigned short	PortC;
	unsigned short	PortS;	
	char	SrcIpv4[32];  
	char	DestIpv4[32];
	char 	SrcIpv6[64];
	char 	DestIpv6[64];
	char	<PERSON><PERSON>[64];
	char	<PERSON><PERSON>[64];
	int 	Spi;	
	char	<PERSON>t[16];
	char	<PERSON><PERSON>[16];
	char	<PERSON>alg[16];
	char	<PERSON>g[16];
} ipsec_set_msg;

typedef struct ipsec_set_sa_msg 
{

	int IsIpv4;			//ipv4=1   ipv6=0	
	char	SrcIpv4[32];  
	char	DestIpv4[32];
	char	SrcIpv6[64];  
	char	DestIpv6[64];
	char	Ikey[64];
	char	Ckey[64];
	int 	Spi;	
	char	XfrmProt[16];	
	char	Mod[16];
	char	Ealg[16];
	char	Alg[16];
} ipsec_set_sa_msg;

typedef struct ipsec_set_sp_msg 
{
	char dir[8];    /*in or out*/
	int IsIpv4;			//ipv4=1   ipv6=0
	unsigned short	PortC;
	unsigned short	PortS;	
	char	SrcIpv4[32];  
	char	DestIpv4[32];
	char	SrcIpv6[64];  
	char	DestIpv6[64];
	int 	Spi;	
	char	XfrmProt[16];
	int		Prot;
	char	Mod[16];
} ipsec_set_sp_msg;

typedef struct ipsec_del_msg 
{
	char dir[8];    /*in or out*/
	int IsIpv4;			//ipv4=1   ipv6=0
	unsigned short	PortC;
	unsigned short	PortS;	
	char	SrcIpv4[32];  
	char	DestIpv4[32];
	char	SrcIpv6[64];  
	char	DestIpv6[64];
	char	Ikey[64];
	char	Ckey[64];
	int 	Spi;	
	char	Prot[16];
	char	Mod[16];
	char	Ealg[16];
	char	Alg[16];
} ipsec_del_msg;

typedef struct ipsec_del_sa_msg
{
	int 	IsIpv4;			//ipv4=1   ipv6=0	
	char	SrcIpv4[32];  
	char	DestIpv4[32];
	char	SrcIpv6[64];  
	char	DestIpv6[64];
	int 	Spi;	
	char	XfrmProt[16];
	char	Mod[16];
}ipsec_del_sa_msg;

typedef struct ipsec_del_sp_msg
{
	char dir[8];    /*in or out*/
	int IsIpv4;			//ipv4=1   ipv6=0
	unsigned short	PortC;
	unsigned short	PortS;	
	char	SrcIpv4[32];  
	char	DestIpv4[32];
	char	SrcIpv6[64];  
	char	DestIpv6[64];	
	int 	Prot;
}ipsec_del_sp_msg;

extern int ipsec_set(int s, void *data, int datalen);
extern int ipsec_del(int s, void *data, int datalen);
extern int ipsec_set_sa(int s, void *data, int datalen);
extern int ipsec_set_sp(int s, void *data, int datalen);
extern int ipsec_del_sa(int s, void *data, int datalen);
extern int ipsec_del_sp(int s, void *data, int datalen);
#endif
