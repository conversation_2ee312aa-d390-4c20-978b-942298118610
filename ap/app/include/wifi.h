#ifndef _WIFI_H_
#define _WIFI_H_

#ifdef CONFIG_RT2860V2_AP_WAPI
#define MAX_NUMBER_OF_MAC               96
#else
#define MAX_NUMBER_OF_MAC               37 /*support max station num is 37-1*/ // if MAX_MBSSID_NUM is 8, this value can't be larger than 211
#endif

typedef union _MACHTTRANSMIT_SETTING {
	struct  {
		unsigned short  MCS: 7; // MCS
		unsigned short  BW: 1;  //channel bandwidth 20MHz or 40 MHz
		unsigned short  ShortGI: 1;
		unsigned short  STBC: 2; //SPACE
		unsigned short	eTxBF: 1;
		unsigned short	rsv: 1;
		unsigned short	iTxBF: 1;
		unsigned short  MODE: 2; // Use definition MODE_xxx.
	} field;
	unsigned short      word;
} MACHTTRANSMIT_SETTING;

typedef struct _RT_802_11_MAC_ENTRY {
	unsigned char			ApIdx;
	unsigned char           Addr[6];
	unsigned char           Aid;
	unsigned char           Psm;     // 0:PWR_ACTIVE, 1:<PERSON><PERSON>_SAVE
	unsigned char           MimoPs;  // 0:MMPS_STATIC, 1:MMPS_DYNAMIC, 3:MMPS_Enabled
	char                    AvgRssi0;
	char                    AvgRssi1;
	char                    AvgRssi2;
	unsigned int            ConnectedTime;
	MACHTTRANSMIT_SETTING	TxRate;
	unsigned int			LastRxRate;
	int						StreamSnr[3];
	int						SoundingRespSnr[3];
	unsigned int            SSID_index; // for multissid station_list nxl

} RT_802_11_MAC_ENTRY;

typedef struct _RT_802_11_MAC_TABLE {
	unsigned long            Num;
	RT_802_11_MAC_ENTRY      Entry[MAX_NUMBER_OF_MAC]; //MAX_LEN_OF_MAC_TABLE = 32
} RT_802_11_MAC_TABLE;

/*add by myc for wifi_client_show 2012-04-19 begin*/
typedef struct _DHCPOFFERADDR {
	unsigned long expires;
	unsigned long ip;
	unsigned char mac[6];
	unsigned char host_name[20];
	unsigned char pad[2];
} DHCPOFFERADDR;


#endif
