#ifndef __LIBCPNV_H
#define __LIBCPNV_H


enum FS_PARTITION_NO {
	FS_IMAGEFS,
	FS_RESOURCEFS,
	FS_NVROFS,
	FS_PARTITION_NO_MAX
};

#define SYSCALL_jffs2_quick_gc_wait_done   385

#define CPNV_OK     (0)
#define CPNV_ERROR (0xFFFFFFFF)

/**
 * @brief 写CP侧NVRW区NV项
 * @param NvItemID      入参，NV项ID
 * @param NvItemData    入参，写入NV项数据缓冲区
 * @param NvItemLen     入参，写入NV项数据缓冲区长度
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning 数据先写到内存，定时刷新到flash。如果要求及时刷新到flash需要再调用cpnv_NvramFlush刷新
 */
extern unsigned int cpnv_NvItemWrite(unsigned int NvItemID, unsigned char *NvItemData, unsigned int NvItemLen);

/**
 * @brief 读取CP侧NVRW区NV项
 * @param NvItemID      入参，NV项ID
 * @param NvItemData    出参，读取NV项数据缓冲区
 * @param NvItemLen     入参，读取NV项数据缓冲区长度
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning
 */
extern unsigned int cpnv_NvItemRead(unsigned int NvItemID, unsigned char *NvItemData, unsigned int NvItemLen);

/**
 * @brief  刷新NVRW区缓存数据到flash
 * @param  无
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning
 */
extern unsigned int cpnv_NvramFlush(void);

/**
 * @brief 写CP侧NVFAC区NV项
 * @param NvItemID      入参，NV项ID
 * @param NvItemData    入参，写入NV项数据缓冲区
 * @param NvItemLen     入参，写入NV项数据缓冲区长度
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning
 */
unsigned int cpnv_NvItemWriteFactory(unsigned int NvItemID, unsigned char *NvItemData, unsigned int NvItemLen);

/**
 * @brief 写CP侧NVRO区NV项
 * @param NvItemID      入参，NV项ID
 * @param NvItemData    入参，写入NV项数据缓冲区
 * @param NvItemLen     入参，写入NV项数据缓冲区长度
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note 调用之前要用cpnv_ChangeNvRoAttr设置为可写
 * @warning 调用后要用cpnv_ChangeNvRoAttr设置为只读，保护NVRO区数据
 */
unsigned int cpnv_NvItemWriteNvro(unsigned int NvItemID, unsigned char *NvItemData, unsigned int NvItemLen);

/**
 * @brief  NV恢复出厂数据
 * @param  无
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning
 */
unsigned int cpnv_ResetNVFactory(void);

/**
 * @brief  设置NVRO区读写属性
 * @param  writable为0设置NVRO区为只读，其他值设置为可写
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning
 */
unsigned int cpnv_ChangeNvRoAttr(int writable);

/**
 * @brief  设置imagefs/resource/nvrofs分区可写属性
 * @param  partition_no取值自enum FS_PARTITION_NO；writable为0设置为只读，其他值设置为可写
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning
 */
unsigned int cpnv_ChangeFsPartitionAttr(int partition_no, int writable);

/**
 * @brief  等待imagefs/resource/nvrofs 文件系统垃圾回收，partition_no取值自enum FS_PARTITION_NO
 * @param  partition_no取值自enum FS_PARTITION_NO
 * @return CPNV_OK 成功，CPNV_ERROR 失败
 * @retval
 * @note
 * @warning 只能在整个该分区文件系统都写完毕后调用才有意义
 */
unsigned int cpnv_FsGcWait(int partition_no);

#endif // __LIBCPNV_H
