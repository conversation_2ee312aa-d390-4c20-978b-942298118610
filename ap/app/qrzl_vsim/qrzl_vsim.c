#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <time.h>
#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
#include <curl/curl.h>
#endif
#include "lib/hash_table.h"
#include "lib/cjson.h"
#include "inc/qrzl_log.h"
#include "inc/qrzl_vsim.h"

// cJSON鍏煎�规€у畯瀹氫箟锛堥€傞厤鏃х増鏈琧JSON锛�
#ifndef cJSON_IsTrue
#define cJSON_IsTrue(item) ((item) && (item)->type == cJSON_True)
#endif

#ifndef cJSON_IsNumber
#define cJSON_IsNumber(item) ((item) && ((item)->type == cJSON_Number))
#endif

#ifndef cJSON_IsFalse
#define cJSON_IsFalse(item) ((item) && (item)->type == cJSON_False)
#endif

#ifndef cJSON_IsNull
#define cJSON_IsNull(item) ((item) && (item)->type == cJSON_NULL)
#endif

#include "softap_api.h"
#include "nv_api.h"
#include "ght_vsim_api.h"
#ifdef JCV_FEATRUE_USE_VSIM_ENGINE
#include "vsim_engine.h"
#elif defined(JCV_FEATRUE_USE_JSON_VSIM_APDU_FROM_ALK_ROOTFS)
#else
#include "vsim.h"
#include <linux/uicc_agt_client.h>
#endif

static void generate_apdu_key(const uint8_t* apdu, uint16_t len, char* key);
static int hex_to_bin(const char* hex, uint8_t* bin, uint16_t* bin_len);
static int process_external_cfun_command(void);


// 鍏ㄥ眬鍙橀噺
static cJSON* apdu_root = NULL;         // APDU JSON 鏍硅妭鐐�
static VsimContext vsim_context = {0};   // VSIM 涓婁笅鏂�
static char* custom_imsi = NULL;         // 鑷�瀹氫箟 IMSI
static char* custom_iccid = NULL;        // 鑷�瀹氫箟 ICCID

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
// HTTP瀹㈡埛绔�鐩稿叧閰嶇疆
static char auth_server_url[256] = "http://123.207.68.245:80/iotHub/api/device/v1/authSync";
static char login_server_url[256] = "http://123.207.68.245:80/iotHub/api/device/v1/login";
static int http_timeout = 10;           // HTTP璇锋眰瓒呮椂鏃堕棿(绉�)
static int max_retry_count = 3;         // 鏈€澶ч噸璇曟�℃暟

// HTTP鍝嶅簲鏁版嵁缁撴瀯
typedef struct {
    char* data;
    size_t size;
} HttpResponse;

// 杩愯惀鍟嗙被鍨嬫灇涓�
typedef enum {
    OPERATOR_UNKNOWN = 0,
    OPERATOR_CMCC = 1,      // 涓�鍥界Щ鍔�
    OPERATOR_CUCC = 2,      // 涓�鍥借仈閫�
    OPERATOR_CTCC = 3       // 涓�鍥界數淇�
} OperatorType;

// 璁惧�囦俊鎭�缁撴瀯
typedef struct {
    char imsi[32];          // IMSI鍙风爜
    char iccid[32];         // ICCID鍙风爜
    char imei[16];          // IMEI鍙风爜
    char device_id[64];     // 璁惧�嘔D
    OperatorType operator;  // 杩愯惀鍟嗙被鍨�
    int is_valid;           // 淇℃伅鏄�鍚︽湁鏁�
} DeviceInfo;

static DeviceInfo device_info = {0};
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

// ====================== 杈呭姪鍑芥暟 ======================

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
// HTTP鍝嶅簲鏁版嵁鍥炶皟鍑芥暟
static size_t http_write_callback(void* contents, size_t size, size_t nmemb, HttpResponse* response) {
    size_t total_size = size * nmemb;
    char* new_data = realloc(response->data, response->size + total_size + 1);

    if (!new_data) {
        qrzl_err("HTTP鍝嶅簲鍐呭瓨鍒嗛厤澶辫触");
        return 0;
    }

    response->data = new_data;
    memcpy(&(response->data[response->size]), contents, total_size);
    response->size += total_size;
    response->data[response->size] = '\0';

    return total_size;
}

// 鍒ゆ柇鏄�鍚︿负閴存潈鐩稿叧鐨凙PDU
static int is_auth_apdu(const uint8_t* apdu, uint16_t len) {
    if (!apdu || len < 4) return 0;

    uint8_t cla = apdu[0];
    uint8_t ins = apdu[1];

    // // 妫€鏌ラ壌鏉冪浉鍏崇殑鍛戒护
    // if (cla == 0x80) {
    //     // CLA=0x80 閫氬父鐢ㄤ簬瀹夊叏鐩稿叧鍛戒护
    //     qrzl_info("妫€娴嬪埌瀹夊叏鍛戒护 CLA=0x80, INS=0x%02X", ins);
    //     return 1;
    // }

    if (ins == 0x88) {
        // AUTHENTICATE 鍛戒护
        qrzl_info("妫€娴嬪埌AUTHENTICATE鍛戒护 INS=0x88");
        return 1;
    }

    // if (ins == 0x20) {
    //     // VERIFY PIN 鍛戒护
    //     qrzl_info("妫€娴嬪埌VERIFY PIN鍛戒护 INS=0x20");
    //     return 1;
    // }

    // 鍙�浠ユ牴鎹�闇€瑕佹坊鍔犳洿澶氶壌鏉傾PDU鐨勫垽鏂�鏉′欢
    return 0;
}

// 鍙戦€侀壌鏉傾PDU鍒版湇鍔″櫒
static int send_auth_apdu_to_server(const uint8_t* apdu_req, uint16_t apdu_req_len,
                                   uint8_t* apdu_rsp, uint16_t* apdu_rsp_len,
                                   uint8_t slot) {
    CURL* curl;
    CURLcode res;
    HttpResponse response = {0};
    int retry_count = 0;
    int success = 0;

    // 鍒濆�嬪寲curl
    curl = curl_easy_init();
    if (!curl) {
        qrzl_err("curl鍒濆�嬪寲澶辫触");
        return -1;
    }

    // 鍒涘缓鏂版牸寮忕殑JSON鏁版嵁
    cJSON* json = cJSON_CreateObject();

    // 娣诲姞璁惧�嘢N
    cJSON_AddStringToObject(json, "sn",
                           device_info.is_valid ? device_info.device_id : "33332513000045");

    // 娣诲姞APDU璇锋眰鏁版嵁锛堝崄鍏�杩涘埗瀛楃�︿覆锛�
    char req_hex[apdu_req_len * 2 + 1];
    generate_apdu_key(apdu_req, apdu_req_len, req_hex);
    cJSON_AddStringToObject(json, "apdu_req", req_hex);

    // 娣诲姞APDU闀垮害锛圠e瀛楁�碉級
    cJSON_AddNumberToObject(json, "apdu_le", apdu_req_len);

    // 娣诲姞璁℃暟鍣�锛堝浐瀹氫负"0001"锛�
    cJSON_AddStringToObject(json, "count", "0001");

    // 娣诲姞ICCID鍜孖MSI
    if (device_info.is_valid) {
        cJSON_AddStringToObject(json, "iccid", device_info.iccid);
        cJSON_AddStringToObject(json, "imsi", device_info.imsi);
    } else {
        // 浣跨敤榛樿�ゅ€�
        cJSON_AddStringToObject(json, "iccid", "9868404791520C200050");
        cJSON_AddStringToObject(json, "imsi", "084906404931180050");
    }

    char* json_string = cJSON_Print(json);
    if (!json_string) {
        qrzl_err("JSON搴忓垪鍖栧け璐�");
        cJSON_Delete(json);
        curl_easy_cleanup(curl);
        return -1;
    }

    qrzl_info("鍑嗗�囧彂閫侀壌鏉傾PDU鍒版湇鍔″櫒: %s", auth_server_url);
    qrzl_info("JSON鏁版嵁: %s", json_string);

    // 閲嶈瘯鏈哄埗
    while (retry_count < max_retry_count && !success) {
        // 閲嶇疆鍝嶅簲鏁版嵁
        if (response.data) {
            free(response.data);
            response.data = NULL;
            response.size = 0;
        }

        // 璁剧疆curl閫夐」
        curl_easy_setopt(curl, CURLOPT_URL, auth_server_url);
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_string);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, http_timeout);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);

        // 璁剧疆HTTP澶�
        struct curl_slist* headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        // 鎵ц�岃�锋眰
        res = curl_easy_perform(curl);

        if (res == CURLE_OK) {
            long response_code;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);

            qrzl_info("HTTP鍝嶅簲鐮�: %ld", response_code);
            qrzl_info("鏈嶅姟鍣ㄥ搷搴�: %s", response.data ? response.data : "鏃犲搷搴旀暟鎹�");

            if (response_code == 200) {
                // 瑙ｆ瀽鍝嶅簲JSON鑾峰彇APDU鏁版嵁
                if (response.data) {
                    cJSON* response_json = cJSON_Parse(response.data);
                    if (response_json) {
                        cJSON* success_field = cJSON_GetObjectItem(response_json, "success");
                        if (success_field && cJSON_IsTrue(success_field)) {
                            cJSON* data = cJSON_GetObjectItem(response_json, "data");
                            if (data) {
                                cJSON* apdu_field = cJSON_GetObjectItem(data, "apdu");
                                if (apdu_field && apdu_field->valuestring) {
                                    // 灏嗗崄鍏�杩涘埗瀛楃�︿覆杞�鎹�涓轰簩杩涘埗鏁版嵁
                                    const char* apdu_hex = apdu_field->valuestring;
                                    int hex_len = strlen(apdu_hex);

                                    if (hex_len % 2 == 0 && hex_len <= (*apdu_rsp_len * 2)) {
                                        uint16_t new_len = hex_len / 2;
                                        if (hex_to_bin(apdu_hex, apdu_rsp, &new_len) == 0) {
                                            *apdu_rsp_len = new_len;
                                            qrzl_info("鎴愬姛瑙ｆ瀽鏈嶅姟鍣ㄨ繑鍥炵殑APDU: %s (闀垮害: %d)",
                                                     apdu_hex, new_len);
                                            success = 1;
                                        } else {
                                            qrzl_err("APDU鍗佸叚杩涘埗杞�鎹㈠け璐�");
                                        }
                                    } else {
                                        qrzl_err("APDU鏁版嵁闀垮害鏃犳晥: %d", hex_len);
                                    }
                                } else {
                                    qrzl_warn("鏈嶅姟鍣ㄥ搷搴斾腑鏈�鎵惧埌apdu瀛楁��");
                                }
                            } else {
                                qrzl_err("鏈嶅姟鍣ㄥ搷搴斾腑缂哄皯data瀛楁��");
                            }
                        } else {
                            cJSON* error_msg = cJSON_GetObjectItem(response_json, "errorMessage");
                            qrzl_err("鏈嶅姟鍣ㄨ繑鍥炲け璐�: %s",
                                    error_msg && error_msg->valuestring ? error_msg->valuestring : "鏈�鐭ラ敊璇�");
                        }
                        cJSON_Delete(response_json);
                    } else {
                        qrzl_err("瑙ｆ瀽鏈嶅姟鍣ㄥ搷搴擩SON澶辫触");
                    }
                }
            } else {
                qrzl_warn("鏈嶅姟鍣ㄨ繑鍥為敊璇�鐮�: %ld", response_code);
            }
        } else {
            qrzl_err("HTTP璇锋眰澶辫触: %s", curl_easy_strerror(res));
        }

        curl_slist_free_all(headers);

        if (!success) {
            retry_count++;
            if (retry_count < max_retry_count) {
                qrzl_info("绗�%d娆￠噸璇�...", retry_count + 1);
                sleep(1); // 绛夊緟1绉掑悗閲嶈瘯
            }
        }
    }

    // 娓呯悊璧勬簮
    if (response.data) {
        free(response.data);
    }
    free(json_string);
    cJSON_Delete(json);
    curl_easy_cleanup(curl);

    if (!success) {
        qrzl_err("閴存潈APDU鍙戦€佸け璐ワ紝宸查噸璇�%d娆�", max_retry_count);
        return -1;
    }

    return 0;
}

// 灏哠IM鍗℃枃浠舵牸寮忕殑IMSI杞�鎹�涓虹湡瀹濱MSI
static void convert_sim_imsi_to_real(const char* sim_imsi, char* real_imsi, size_t size) {
    if (!sim_imsi || !real_imsi || size < 16) {
        return;
    }

    // SIM鍗℃枃浠舵牸寮忕殑IMSI鏄疊CD缂栫爜锛岄渶瑕佽浆鎹�涓虹湡瀹炴牸寮�
    // 渚嬪��: "084906111124810" -> "460011112481"

    int len = strlen(sim_imsi);
    if (len < 3) {
        // 闀垮害澶�鐭�锛岀洿鎺ュ�嶅埗
        strncpy(real_imsi, sim_imsi, size - 1);
        real_imsi[size - 1] = '\0';
        return;
    }

    int real_idx = 0;
    int i;

    // IMSI鐨凚CD缂栫爜鏍煎紡锛�
    // 绗�涓€涓�瀛楄妭锛氶暱搴� + 濂囧伓鏍囧織
    // 浠庣��浜屼釜瀛楄妭寮€濮嬫槸鐪熸�ｇ殑IMSI鏁版嵁

    // 璺宠繃绗�涓€涓�瀛楄妭锛堥暱搴︽寚绀猴級
    // 浠庣��浜屼釜瀛楄妭寮€濮嬪�勭悊锛屼絾瑕佺壒娈婂�勭悊绗�涓€涓�鍗婂瓧鑺�
    if (len >= 4) {
        // 绗�浜屼釜瀛楄妭鐨勪綆4浣嶆槸IMSI鐨勭��涓€浣嶆暟瀛�
        char first_digit = sim_imsi[3]; // 绗�浜屼釜瀛楄妭鐨勪綆4浣�
        if (first_digit != 'F' && first_digit != 'f') {
            real_imsi[real_idx++] = first_digit;
        }

        // 绗�浜屼釜瀛楄妭鐨勯珮4浣嶆槸IMSI鐨勭��浜屼綅鏁板瓧
        char second_digit = sim_imsi[2]; // 绗�浜屼釜瀛楄妭鐨勯珮4浣�
        if (second_digit != 'F' && second_digit != 'f') {
            real_imsi[real_idx++] = second_digit;
        }
    }

    // 浠庣��涓変釜瀛楄妭寮€濮嬶紝姝ｅ父浜ゆ崲澶勭悊
    for (i = 4; i < len && real_idx < size - 1; i += 2) {
        if (i + 1 < len) {
            // 浜ゆ崲姣忓�笲CD鏁板瓧
            char low_digit = sim_imsi[i + 1];
            char high_digit = sim_imsi[i];

            if (low_digit != 'F' && low_digit != 'f' && real_idx < size - 1) {
                real_imsi[real_idx++] = low_digit;
            }
            if (high_digit != 'F' && high_digit != 'f' && real_idx < size - 1) {
                real_imsi[real_idx++] = high_digit;
            }
        }
    }

    real_imsi[real_idx] = '\0';

    qrzl_info("IMSI杞�鎹㈣皟璇�: SIM鏍煎紡='%s' -> 鐪熷疄鏍煎紡='%s'", sim_imsi, real_imsi);
}

// 灏哠IM鍗℃枃浠舵牸寮忕殑ICCID杞�鎹�涓虹湡瀹濱CCID
static void convert_sim_iccid_to_real(const char* sim_iccid, char* real_iccid, size_t size) {
    if (!sim_iccid || !real_iccid || size < 21) {
        return;
    }

    // SIM鍗℃枃浠舵牸寮忕殑ICCID涔熸槸BCD缂栫爜锛岄渶瑕佽浆鎹�
    // 渚嬪��: "9868404791520C200050" -> "89864047915202000050"

    int len = strlen(sim_iccid);
    if (len < 20) {
        // 濡傛灉闀垮害涓嶅�燂紝鐩存帴澶嶅埗
        strncpy(real_iccid, sim_iccid, size - 1);
        real_iccid[size - 1] = '\0';
        return;
    }

    // BCD瑙ｇ爜锛氫氦鎹㈡瘡瀵规暟瀛楃殑浣嶇疆
    int real_idx = 0;
    int i;

    for (i = 0; i < len && real_idx < size - 1; i += 2) {
        if (i + 1 < len) {
            // 浜ゆ崲姣忓�笲CD鏁板瓧
            real_iccid[real_idx++] = sim_iccid[i + 1];
            if (real_idx < size - 1) {
                real_iccid[real_idx++] = sim_iccid[i];
            }
        }
    }

    real_iccid[real_idx] = '\0';

    // 绉婚櫎鏈�灏剧殑濉�鍏匜鍜孋
    int real_len = strlen(real_iccid);
    while (real_len > 0 && (real_iccid[real_len - 1] == 'F' ||
                           real_iccid[real_len - 1] == 'f' ||
                           real_iccid[real_len - 1] == 'C' ||
                           real_iccid[real_len - 1] == 'c')) {
        real_iccid[--real_len] = '\0';
    }
}

// 鏍规嵁ispId璇嗗埆杩愯惀鍟嗙被鍨�
static OperatorType identify_operator_by_isp_id(int isp_id) {
    switch (isp_id) {
        case 1:
            qrzl_info("鏍规嵁ispId璇嗗埆涓轰腑鍥界Щ鍔� (ispId: %d)", isp_id);
            return OPERATOR_CMCC;
        case 2:
            qrzl_info("鏍规嵁ispId璇嗗埆涓轰腑鍥借仈閫� (ispId: %d)", isp_id);
            return OPERATOR_CUCC;
        case 3:
            qrzl_info("鏍规嵁ispId璇嗗埆涓轰腑鍥界數淇� (ispId: %d)", isp_id);
            return OPERATOR_CTCC;
        default:
            qrzl_warn("鏈�鐭ョ殑ispId: %d", isp_id);
            return OPERATOR_UNKNOWN;
    }
}

// 鏍规嵁IMSI璇嗗埆杩愯惀鍟嗙被鍨�
static OperatorType identify_operator_by_imsi(const char* imsi) {
    if (!imsi || strlen(imsi) < 6) {
        qrzl_warn("IMSI鏍煎紡鏃犳晥: %s", imsi ? imsi : "NULL");
        return OPERATOR_UNKNOWN;
    }

    // 鎻愬彇IMSI鍓�6浣嶏紙MCC+MNC锛�
    char mcc_mnc[7] = {0};
    strncpy(mcc_mnc, imsi, 6);

    qrzl_info("鍒嗘瀽IMSI: %s, MCC+MNC: %s", imsi, mcc_mnc);

    // 涓�鍥界Щ鍔� (China Mobile)
    // 460000, 460002, 460004, 460007, 460008
    if (strncmp(mcc_mnc, "460000", 6) == 0 ||
        strncmp(mcc_mnc, "460002", 6) == 0 ||
        strncmp(mcc_mnc, "460004", 6) == 0 ||
        strncmp(mcc_mnc, "460007", 6) == 0 ||
        strncmp(mcc_mnc, "460008", 6) == 0) {
        qrzl_info("璇嗗埆涓轰腑鍥界Щ鍔�");
        return OPERATOR_CMCC;
    }

    // 涓�鍥借仈閫� (China Unicom)
    // 460001, 460006, 460009
    if (strncmp(mcc_mnc, "460001", 6) == 0 ||
        strncmp(mcc_mnc, "460006", 6) == 0 ||
        strncmp(mcc_mnc, "460009", 6) == 0) {
        qrzl_info("璇嗗埆涓轰腑鍥借仈閫�");
        return OPERATOR_CUCC;
    }

    // 涓�鍥界數淇� (China Telecom)
    // 460003, 460005, 460011
    if (strncmp(mcc_mnc, "460003", 6) == 0 ||
        strncmp(mcc_mnc, "460005", 6) == 0 ||
        strncmp(mcc_mnc, "460011", 6) == 0) {
        qrzl_info("璇嗗埆涓轰腑鍥界數淇�");
        return OPERATOR_CTCC;
    }

    qrzl_warn("鏈�鐭ョ殑杩愯惀鍟哅CC+MNC: %s", mcc_mnc);
    return OPERATOR_UNKNOWN;
}

// 鑾峰彇杩愯惀鍟嗗�瑰簲鐨凧SON鏂囦欢鍚�
static const char* get_operator_json_file(OperatorType operator) {
    switch (operator) {
        case OPERATOR_CMCC:
            return "/etc/cmcc.json";
        case OPERATOR_CUCC:
            return "/etc/cucc.json";
        case OPERATOR_CTCC:
            return "/etc/ctcc.json";
        default:
            return "/etc/cmcc.json"; // 榛樿�や娇鐢ㄧЩ鍔�
    }
}

// 鑾峰彇杩愯惀鍟嗗悕绉�
static const char* get_operator_name(OperatorType operator) {
    switch (operator) {
        case OPERATOR_CMCC:
            return "涓�鍥界Щ鍔�";
        case OPERATOR_CUCC:
            return "涓�鍥借仈閫�";
        case OPERATOR_CTCC:
            return "涓�鍥界數淇�";
        default:
            return "鏈�鐭ヨ繍钀ュ晢";
    }
}

// 灏咺CCID杞�鎹�涓篠IM鍗″瓨鍌ㄦ牸寮忥紙BCD缂栫爜锛屽瓧鑺備氦鎹�锛�
static void iccid_to_sim_format(const char* iccid, char* sim_format) {
    if (!iccid || !sim_format) return;

    int len = strlen(iccid);
    int i, j = 0;

    // ICCID鍦⊿IM鍗′腑浠�BCD鏍煎紡瀛樺偍锛岄渶瑕佸瓧鑺傚唴浜ゆ崲
    for (i = 0; i < len; i += 2) {
        if (i + 1 < len) {
            // 浜ゆ崲瀛楄妭鍐呯殑涓や釜鏁板瓧
            sprintf(sim_format + j, "%c%c", iccid[i + 1], iccid[i]);
            j += 2;
        } else {
            // 濂囨暟闀垮害锛屾渶鍚庝竴浣嶈ˉF
            sprintf(sim_format + j, "F%c", iccid[i]);
            j += 2;
        }
    }

    // 娣诲姞鐘舵€佸瓧9000
    strcpy(sim_format + j, "9000");

    qrzl_info("ICCID杞�鎹�: %s -> %s", iccid, sim_format);
}

// 灏咺MSI杞�鎹�涓篠IM鍗″瓨鍌ㄦ牸寮�
static void imsi_to_sim_format(const char* imsi, char* sim_format) {
    if (!imsi || !sim_format) return;

    int len = strlen(imsi);
    char temp[32] = {0};

    // IMSI鏍煎紡锛氶暱搴� + 濂囧伓鏍囧織 + BCD缂栫爜鐨処MSI
    sprintf(temp, "08%02X", len); // 08鏄�闀垮害锛岀��浜屼釜瀛楄妭鏄疘MSI闀垮害

    // 娣诲姞濂囧伓鏍囧織锛堢��涓€涓�鍗婂瓧鑺傦級鍜孧CC鐨勭��涓€浣�
    if (len > 0) {
        sprintf(temp + 4, "%c9", imsi[0]); // 9琛ㄧず濂囨暟闀垮害
    }

    // 杞�鎹㈠墿浣欑殑IMSI涓築CD鏍煎紡锛堝瓧鑺傚唴浜ゆ崲锛�
    int i, j = 6;
    for (i = 1; i < len; i += 2) {
        if (i + 1 < len) {
            sprintf(temp + j, "%c%c", imsi[i + 1], imsi[i]);
            j += 2;
        } else {
            sprintf(temp + j, "F%c", imsi[i]);
            j += 2;
        }
    }

    // 娣诲姞鐘舵€佸瓧9000
    strcpy(temp + j, "9000");
    strcpy(sim_format, temp);

    qrzl_info("IMSI杞�鎹�: %s -> %s", imsi, sim_format);
}

// 妫€鏌�APDU鏄�鍚︿负ICCID璇诲彇鍛戒护
static int is_iccid_read_apdu(const uint8_t* apdu, uint16_t len) {
    // SELECT 2FE2 + READ BINARY
    // 鎴栬€呯洿鎺ョ殑READ BINARY鍦�2FE2鏂囦欢涓婁笅鏂囦腑
    if (len >= 5 && apdu[0] == 0x00 && apdu[1] == 0xB0) {
        // 妫€鏌ュ綋鍓嶄笂涓嬫枃鏄�鍚︿负2FE2
        if (vsim_context.current_path && strstr(vsim_context.current_path, "2FE2")) {
            return 1;
        }
    }
    return 0;
}

// 妫€鏌�APDU鏄�鍚︿负IMSI璇诲彇鍛戒护
static int is_imsi_read_apdu(const uint8_t* apdu, uint16_t len) {
    // SELECT 6F07 + READ BINARY
    // 鎴栬€呯洿鎺ョ殑READ BINARY鍦�6F07鏂囦欢涓婁笅鏂囦腑
    if (len >= 5 && apdu[0] == 0x00 && apdu[1] == 0xB0) {
        // 妫€鏌ュ綋鍓嶄笂涓嬫枃鏄�鍚︿负6F07
        if (vsim_context.current_path && strstr(vsim_context.current_path, "6F07")) {
            return 1;
        }
    }
    return 0;
}

// 鐢熸垚鍔ㄦ€両CCID鍝嶅簲
static int generate_dynamic_iccid_response(uint8_t* apdu_rsp, uint16_t* apdu_rsp_len) {
    if (!device_info.is_valid || strlen(device_info.iccid) == 0) {
        return -1; // 娌℃湁鏈夋晥鐨処CCID鏁版嵁
    }

    // 鐩存帴浣跨敤device_info.iccid锛屽苟娣诲姞鐘舵€佸瓧9000
    char iccid_with_status[64] = {0};
    snprintf(iccid_with_status, sizeof(iccid_with_status), "%s9000", device_info.iccid);

    qrzl_info("浣跨敤鏈嶅姟鍣ㄤ笅鍙戠殑ICCID: %s", device_info.iccid);
    qrzl_info("娣诲姞鐘舵€佸瓧鍚�: %s", iccid_with_status);

    // 灏嗗崄鍏�杩涘埗瀛楃�︿覆杞�鎹�涓轰簩杩涘埗鏁版嵁
    int result = hex_to_bin(iccid_with_status, apdu_rsp, apdu_rsp_len);
    if (result == 0) {
        qrzl_info("ICCID APDU杞�鎹㈡垚鍔燂紝浜岃繘鍒堕暱搴�: %d", *apdu_rsp_len);
        // 鎵撳嵃鍓嶅嚑涓�瀛楄妭鐢ㄤ簬璋冭瘯
        if (*apdu_rsp_len >= 4) {
            qrzl_info("ICCID APDU浜岃繘鍒跺墠4瀛楄妭: %02X %02X %02X %02X",
                      apdu_rsp[0], apdu_rsp[1], apdu_rsp[2], apdu_rsp[3]);
        }
    } else {
        qrzl_err("ICCID APDU杞�鎹㈠け璐�");
    }

    return result;
}

// 鐢熸垚鍔ㄦ€両MSI鍝嶅簲
static int generate_dynamic_imsi_response(uint8_t* apdu_rsp, uint16_t* apdu_rsp_len) {
    if (!device_info.is_valid || strlen(device_info.imsi) == 0) {
        return -1; // 娌℃湁鏈夋晥鐨処MSI鏁版嵁
    }

    // 鐩存帴浣跨敤device_info.imsi锛屽苟娣诲姞鐘舵€佸瓧9000
    char imsi_with_status[64] = {0};
    snprintf(imsi_with_status, sizeof(imsi_with_status), "%s9000", device_info.imsi);

    qrzl_info("浣跨敤鏈嶅姟鍣ㄤ笅鍙戠殑IMSI: %s", device_info.imsi);
    qrzl_info("娣诲姞鐘舵€佸瓧鍚�: %s", imsi_with_status);

    // 灏嗗崄鍏�杩涘埗瀛楃�︿覆杞�鎹�涓轰簩杩涘埗鏁版嵁
    int result = hex_to_bin(imsi_with_status, apdu_rsp, apdu_rsp_len);
    if (result == 0) {
        qrzl_info("IMSI APDU杞�鎹㈡垚鍔燂紝浜岃繘鍒堕暱搴�: %d", *apdu_rsp_len);
    } else {
        qrzl_err("IMSI APDU杞�鎹㈠け璐�");
    }

    return result;
}

// 鑾峰彇璁惧�嘙AC鍦板潃
static int get_device_mac_address(char* mac_addr, size_t size) {
    FILE* fp;
    char buffer[256];

    // 灏濊瘯浠庣綉缁滄帴鍙ｈ幏鍙朚AC鍦板潃
    fp = popen("cat /sys/class/net/*/address 2>/dev/null | head -1", "r");
    if (fp) {
        if (fgets(buffer, sizeof(buffer), fp)) {
            // 绉婚櫎鎹㈣�岀��
            buffer[strcspn(buffer, "\n")] = 0;
            strncpy(mac_addr, buffer, size - 1);
            mac_addr[size - 1] = '\0';
            pclose(fp);
            return 0;
        }
        pclose(fp);
    }

    // 濡傛灉鑾峰彇澶辫触锛屼娇鐢ㄩ粯璁ゅ€�
    strncpy(mac_addr, "3C:68:01:6B:53:B3", size - 1);
    mac_addr[size - 1] = '\0';
    return -1;
}

// 鑾峰彇璁惧�囧簭鍒楀彿
static int get_device_serial_number(char* sn, size_t size) {
    FILE* fp;
    char buffer[256];

    // 灏濊瘯浠庣郴缁熻幏鍙栧簭鍒楀彿
    fp = fopen("/proc/cpuinfo", "r");
    if (fp) {
        while (fgets(buffer, sizeof(buffer), fp)) {
            if (strstr(buffer, "Serial")) {
                char* colon = strchr(buffer, ':');
                if (colon) {
                    colon++;
                    while (*colon == ' ' || *colon == '\t') colon++;
                    buffer[strcspn(colon, "\n")] = 0;
                    strncpy(sn, colon, size - 1);
                    sn[size - 1] = '\0';
                    fclose(fp);
                    return 0;
                }
            }
        }
        fclose(fp);
    }

    // 濡傛灉鑾峰彇澶辫触锛屼娇鐢ㄩ粯璁ゅ€�
    strncpy(sn, "33332513000045", size - 1);
    sn[size - 1] = '\0';
    return -1;
}

// 璁惧�囩櫥褰曡幏鍙朣IM鍗′俊鎭� - 浣跨敤鍥哄畾鏁版嵁娴嬭瘯
static int device_login_and_get_sim_info(void) {
    CURL* curl;
    CURLcode res;
    HttpResponse response = {0};
    int retry_count = 0;
    int success = 0;

    qrzl_info("寮€濮嬭�惧�囩櫥褰曟祦绋嬶紙浣跨敤鍥哄畾娴嬭瘯鏁版嵁锛�...");

    // 鍒濆�嬪寲curl
    curl = curl_easy_init();
    if (!curl) {
        qrzl_err("curl鍒濆�嬪寲澶辫触");
        return -1;
    }

    // 浣跨敤鎮ㄦ彁渚涚殑鍥哄畾JSON鏁版嵁杩涜�屾祴璇�
    const char* json_string = "{"
        "\"sn\":\"33332513000045\","
        "\"androidId\":\"\","
        "\"boardName\":\"MZ803\","
        "\"devcie_con_num\":0,"
        "\"deviceBrand\":\"ZXIC\","
        "\"electric\":\"100\","
        "\"packageName\":\"\","
        "\"getSimNum\":1,"
        "\"ssid\":\"JH-000021\","
        "\"wifipwd\":\"12345678\","
        "\"systemVersion\":\"MZ804LD1.0_KSBC_COMMON_SL_V2.01.01.02P48U05_02-250616\","
        "\"versionCode\":\"03\","
        "\"versionName\":\"MZ804LD1.0_KSBC_COMMON_SL_V2.01.01.02P48U05_02-250616\","
        "\"wifiMac\":\"3C:68:01:6B:53:B3\","
        "\"seed_card_iccid\":\"89861124207033765695\","
        "\"seed_card_imei\":\"865214070000121\","
        "\"seed_card_imsi\":\"***************\","
        "\"phoneType\":\"GSM\","
        "\"cdma\":\"\","
        "\"gsm\":\"460,11,9561,127398418,-57\""
        "}";

    qrzl_info("鍑嗗�囧彂閫佺櫥褰曡�锋眰鍒�: %s", login_server_url);
    qrzl_info("浣跨敤鍥哄畾JSON鏁版嵁: %s", json_string);

    // 閲嶈瘯鏈哄埗
    while (retry_count < max_retry_count && !success) {
        // 閲嶇疆鍝嶅簲鏁版嵁
        if (response.data) {
            free(response.data);
            response.data = NULL;
            response.size = 0;
        }

        // 璁剧疆curl閫夐」
        curl_easy_setopt(curl, CURLOPT_URL, login_server_url);
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_string);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, http_timeout);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);

        // 璁剧疆HTTP澶�
        struct curl_slist* headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        // 鎵ц�岃�锋眰
        res = curl_easy_perform(curl);

        if (res == CURLE_OK) {
            long response_code;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);

            qrzl_info("HTTP鍝嶅簲鐮�: %ld", response_code);
            qrzl_info("鏈嶅姟鍣ㄥ搷搴�: %s", response.data ? response.data : "鏃犲搷搴旀暟鎹�");

            if (response_code == 200) {
                // 瑙ｆ瀽鍝嶅簲JSON鑾峰彇SIM鍗′俊鎭�
                if (response.data) {
                    cJSON* response_json = cJSON_Parse(response.data);
                    if (response_json) {
                        // 妫€鏌�success瀛楁��
                        cJSON* success_field = cJSON_GetObjectItem(response_json, "success");
                        if (success_field && cJSON_IsTrue(success_field)) {
                            cJSON* data = cJSON_GetObjectItem(response_json, "data");
                            if (data) {
                                // 鑾峰彇鍩烘湰SIM淇℃伅
                                cJSON* imsi = cJSON_GetObjectItem(data, "imsi");
                                cJSON* iccid = cJSON_GetObjectItem(data, "iccid");
                                cJSON* imei = cJSON_GetObjectItem(data, "imei");
                                cJSON* sn = cJSON_GetObjectItem(data, "sn");
                                cJSON* isp_id = cJSON_GetObjectItem(data, "ispId");

                                // 淇濆瓨鑾峰彇鍒扮殑SIM淇℃伅
                                if (imsi && imsi->valuestring) {
                                    strncpy(device_info.imsi, imsi->valuestring, sizeof(device_info.imsi) - 1);
                                    device_info.imsi[sizeof(device_info.imsi) - 1] = '\0';
                                    success = 1;
                                }
                                if (iccid && iccid->valuestring) {
                                    strncpy(device_info.iccid, iccid->valuestring, sizeof(device_info.iccid) - 1);
                                    device_info.iccid[sizeof(device_info.iccid) - 1] = '\0';
                                }
                                if (imei && imei->valuestring) {
                                    strncpy(device_info.imei, imei->valuestring, sizeof(device_info.imei) - 1);
                                    device_info.imei[sizeof(device_info.imei) - 1] = '\0';
                                }
                                if (sn && sn->valuestring) {
                                    strncpy(device_info.device_id, sn->valuestring, sizeof(device_info.device_id) - 1);
                                    device_info.device_id[sizeof(device_info.device_id) - 1] = '\0';
                                } else {
                                    strcpy(device_info.device_id, "33332513000045"); // 浣跨敤鍥哄畾SN
                                }

                                if (success) {
                                    // 浼樺厛浣跨敤ispId璇嗗埆杩愯惀鍟嗭紝濡傛灉娌℃湁鍒欎娇鐢↖MSI
                                    if (isp_id && cJSON_IsNumber(isp_id)) {
                                        device_info.operator = identify_operator_by_isp_id(isp_id->valueint);
                                    } else {
                                        qrzl_info("鏈�鎵惧埌ispId瀛楁�碉紝浣跨敤IMSI璇嗗埆杩愯惀鍟�");
                                        device_info.operator = identify_operator_by_imsi(device_info.imsi);
                                    }
                                    device_info.is_valid = 1;

                                    // 杞�鎹�SIM鍗℃枃浠舵牸寮忎负鐪熷疄鏍煎紡鐢ㄤ簬鏄剧ず
                                    char real_imsi[16] = {0};
                                    char real_iccid[21] = {0};
                                    convert_sim_imsi_to_real(device_info.imsi, real_imsi, sizeof(real_imsi));
                                    convert_sim_iccid_to_real(device_info.iccid, real_iccid, sizeof(real_iccid));

                                    qrzl_info("鑾峰彇鍒癝IM鍗′俊鎭�:");
                                    qrzl_info("  IMSI (SIM鏍煎紡): %s", device_info.imsi);
                                    qrzl_info("  IMSI (鐪熷疄鏍煎紡): %s", real_imsi);
                                    qrzl_info("  ICCID (SIM鏍煎紡): %s", device_info.iccid);
                                    qrzl_info("  ICCID (鐪熷疄鏍煎紡): %s", real_iccid);
                                    qrzl_info("  IMEI: %s", device_info.imei);
                                    qrzl_info("  璁惧�嘔D: %s", device_info.device_id);
                                    if (isp_id && cJSON_IsNumber(isp_id)) {
                                        qrzl_info("  ispId: %d", isp_id->valueint);
                                    }
                                    qrzl_info("  杩愯惀鍟�: %s", get_operator_name(device_info.operator));

                                    // 璁板綍鍏朵粬鏈夌敤淇℃伅
                                    cJSON* heart_beat_times = cJSON_GetObjectItem(data, "heartBeatTimes");
                                    cJSON* electric_warn_val = cJSON_GetObjectItem(data, "electricWarnVal");
                                    cJSON* ssid = cJSON_GetObjectItem(data, "ssid");
                                    cJSON* pwd = cJSON_GetObjectItem(data, "pwd");
                                    cJSON* apn = cJSON_GetObjectItem(data, "apn");

                                    if (heart_beat_times && cJSON_IsNumber(heart_beat_times)) {
                                        qrzl_info("  蹇冭烦闂撮殧: %d绉�", heart_beat_times->valueint);
                                    }
                                    if (electric_warn_val && cJSON_IsNumber(electric_warn_val)) {
                                        qrzl_info("  鐢甸噺璀﹀憡鍊�: %d%%", electric_warn_val->valueint);
                                    }
                                    if (ssid && ssid->valuestring) {
                                        qrzl_info("  WiFi SSID: %s", ssid->valuestring);
                                    }
                                    if (apn && apn->valuestring) {
                                        qrzl_info("  APN: %s", apn->valuestring);
                                    }
                                } else {
                                    qrzl_warn("鏈嶅姟鍣ㄥ搷搴斾腑鏈�鎵惧埌鏈夋晥鐨処MSI淇℃伅");
                                }
                            } else {
                                qrzl_err("鏈嶅姟鍣ㄥ搷搴斾腑缂哄皯data瀛楁��");
                            }
                        } else {
                            cJSON* error_msg = cJSON_GetObjectItem(response_json, "errorMessage");
                            qrzl_err("鏈嶅姟鍣ㄨ繑鍥炲け璐�: %s",
                                    error_msg && error_msg->valuestring ? error_msg->valuestring : "鏈�鐭ラ敊璇�");
                        }

                        cJSON_Delete(response_json);
                    } else {
                        qrzl_err("瑙ｆ瀽鏈嶅姟鍣ㄥ搷搴擩SON澶辫触");
                    }
                }
            } else {
                qrzl_warn("鏈嶅姟鍣ㄨ繑鍥為敊璇�鐮�: %ld", response_code);
            }
        } else {
            qrzl_err("HTTP鐧诲綍璇锋眰澶辫触: %s", curl_easy_strerror(res));
        }

        curl_slist_free_all(headers);

        if (!success) {
            retry_count++;
            if (retry_count < max_retry_count) {
                qrzl_info("鐧诲綍绗�%d娆￠噸璇�...", retry_count + 1);
                sleep(2); // 绛夊緟2绉掑悗閲嶈瘯
            }
        }
    }

    // 娓呯悊璧勬簮
    if (response.data) {
        free(response.data);
    }
    curl_easy_cleanup(curl);

    if (!success) {
        qrzl_err("璁惧�囩櫥褰曞け璐ワ紝宸查噸璇�%d娆�", max_retry_count);
        return -1;
    }

    return 0;
}
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

// 鐢熸垚 APDU 璇锋眰鐨勫崄鍏�杩涘埗閿�
static void generate_apdu_key(const uint8_t* apdu, uint16_t len, char* key) {
    int i;
    for (i = 0; i < len; i++) {
        sprintf(key + 2*i, "%02X", apdu[i]);
    }
    key[2*len] = '\0';
}

// 鍗佸叚杩涘埗瀛楃�︿覆杞�浜岃繘鍒�
static int hex_to_bin(const char* hex, uint8_t* bin, uint16_t* bin_len) {
    if (!hex || !bin || !bin_len) return -1;
    
    size_t hex_len = strlen(hex);
    if (hex_len % 2 != 0) {
        qrzl_err("鏃犳晥鐨勫崄鍏�杩涘埗闀垮害: %zu (蹇呴』鏄�鍋舵暟)", hex_len);
        return -1;
    }
    
    *bin_len = hex_len / 2;
    
    size_t i;
    for (i = 0; i < *bin_len; i++) {
        if (sscanf(hex + 2*i, "%2hhx", &bin[i]) != 1) {
            qrzl_err("鍗佸叚杩涘埗杞�鎹㈠け璐�: %s", hex);
            return -1;
        }
    }
    return 0;
}

// 閲嶇疆 VSIM 涓婁笅鏂�
static void reset_vsim_context() {
    if (vsim_context.current_path) {
        free(vsim_context.current_path);
        vsim_context.current_path = NULL;
    }
    vsim_context.current_node = apdu_root;  // 閲嶇疆鍒版牴鑺傜偣
    qrzl_info("VSIM 涓婁笅鏂囧凡閲嶇疆");
}

// 澶勭悊 JSON 鑺傜偣鍝嶅簲
static int handle_json_node(cJSON* node, uint8_t* apdu_rsp, uint16_t* apdu_rsp_len) {
    if (!node || !apdu_rsp || !apdu_rsp_len) return -1;
    
    if (node && (node->type & 255) == cJSON_String) {
        // 鐩存帴瀛楃�︿覆鍝嶅簲
        return hex_to_bin(node->valuestring, apdu_rsp, apdu_rsp_len);
    }
    else if (node && (node->type & 255) == cJSON_Object) {
        // 瀵硅薄鑺傜偣锛氬寘鍚� response 鍜� children
        cJSON* response = cJSON_GetObjectItem(node, "response");
        if (response && (response->type & 255) == cJSON_String) {
            return hex_to_bin(response->valuestring, apdu_rsp, apdu_rsp_len);
        }
    }
    
    qrzl_err("鏃犳晥鐨� JSON 鑺傜偣绫诲瀷");
    return -1;
}

// 鎵撳嵃鍗佸叚杩涘埗鏁版嵁
static void print_hex(const char* label, const uint8_t* data, uint16_t len) {
    if (!label || !data || len == 0) return;
    
    char hex_str[3 * len + 1];
    char *ptr = hex_str;
    
    int i;
    for (i = 0; i < len && i < 256; i++) {
        ptr += sprintf(ptr, "%02X ", data[i]);
    }
    *ptr = '\0';
    
    qrzl_info("%s (闀垮害 %d): %s", label, len, hex_str);
}

// ====================== APDU 澶勭悊鏍稿績 ======================
static int vsim_apdu_handler(uint8_t* apdu_req, uint16_t apdu_req_len,
                             uint8_t* apdu_rsp, uint16_t* apdu_rsp_len,
                             uint8_t slot)
{
#ifdef JCV_FEATRUE_USE_VSIM_ENGINE
    t_uicc_apduheader c_apdu;
    t_uicc_apdufooter r_apdu;
    unsigned char data_buf[UICC_APDU_LEN];
    T_VSIM_COMMANDCASE cmd_case;

    qrzl_info("===== [VSIM ENGINE] APDU Handling Start (Slot %d) =====", slot);
    print_hex("APDU REQ", apdu_req, apdu_req_len);

    // Parse APDU
    c_apdu.cla = apdu_req[0];
    c_apdu.ins = apdu_req[1];
    c_apdu.p1 = apdu_req[2];
    c_apdu.p2 = apdu_req[3];
    
    if (apdu_req_len == 4) {
        cmd_case = DRV_VSIM_CMD_CASE_1;
        c_apdu.lc = 0;
        c_apdu.le = 0;
    } else if (apdu_req_len == 5) {
        cmd_case = DRV_VSIM_CMD_CASE_2;
        c_apdu.lc = 0;
        c_apdu.le = apdu_req[4];
    } else {
        c_apdu.lc = apdu_req[4];
        if (apdu_req_len == 5 + c_apdu.lc) {
            cmd_case = DRV_VSIM_CMD_CASE_3;
            c_apdu.le = 0;
            memcpy(data_buf, &apdu_req[5], c_apdu.lc);
        } else if (apdu_req_len == 6 + c_apdu.lc) {
            cmd_case = DRV_VSIM_CMD_CASE_4;
            c_apdu.le = apdu_req[5 + c_apdu.lc];
            memcpy(data_buf, &apdu_req[5], c_apdu.lc);
        } else {
            qrzl_err("Invalid APDU length");
            return -1;
        }
    }

    // Call our vsim engine
    zVcard_TransportApdu(slot, cmd_case, 0, c_apdu, &r_apdu, data_buf);

    // Copy response
    memcpy(apdu_rsp, data_buf, r_apdu.luicc);
    apdu_rsp[r_apdu.luicc] = r_apdu.sw1;
    apdu_rsp[r_apdu.luicc + 1] = r_apdu.sw2;
    *apdu_rsp_len = r_apdu.luicc + 2;

    print_hex("APDU RSP", apdu_rsp, *apdu_rsp_len);
    qrzl_info("===== [VSIM ENGINE] APDU Handling End =====");
    return 0;
#elif defined(JCV_FEATRUE_USE_JSON_VSIM_APDU_FROM_ALK_ROOTFS)
    char req_key[512] = {0};
    generate_apdu_key(apdu_req, apdu_req_len, req_key);

    qrzl_info("===== [VSIM Slot %d] APDU Request (ALK_ROOTFS) =====", slot);
    print_hex("APDU REQ", apdu_req, apdu_req_len);
    qrzl_info("REQ KEY: %s", req_key);

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
    // 浼樺厛妫€鏌ユ槸鍚︿负ICCID/IMSI璇诲彇鍛戒护锛屽�傛灉鏄�鍒欎娇鐢ㄥ姩鎬佹暟鎹�
    if (is_iccid_read_apdu(apdu_req, apdu_req_len)) {
        qrzl_info("妫€娴嬪埌ICCID璇诲彇鍛戒护锛屼娇鐢ㄦ湇鍔″櫒涓嬪彂鐨勫姩鎬両CCID鏁版嵁");
        if (generate_dynamic_iccid_response(apdu_rsp, apdu_rsp_len) == 0) {
            print_hex("APDU RSP (Dynamic ICCID)", apdu_rsp, *apdu_rsp_len);
            return 0;
        } else {
            qrzl_warn("鍔ㄦ€両CCID鏁版嵁鐢熸垚澶辫触锛岀户缁�浣跨敤JSON琛ㄦ暟鎹�");
        }
    }

    if (is_imsi_read_apdu(apdu_req, apdu_req_len)) {
        qrzl_info("妫€娴嬪埌IMSI璇诲彇鍛戒护锛屼娇鐢ㄦ湇鍔″櫒涓嬪彂鐨勫姩鎬両MSI鏁版嵁");
        if (generate_dynamic_imsi_response(apdu_rsp, apdu_rsp_len) == 0) {
            print_hex("APDU RSP (Dynamic IMSI)", apdu_rsp, *apdu_rsp_len);
            return 0;
        } else {
            qrzl_warn("鍔ㄦ€両MSI鏁版嵁鐢熸垚澶辫触锛岀户缁�浣跨敤JSON琛ㄦ暟鎹�");
        }
    }
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

    // SELECT MF (3F00) resets the context
    if (apdu_req_len >= 7 && apdu_req[0] == 0x00 && apdu_req[1] == 0xA4 &&
        memcmp(apdu_req + 5, "\x3F\x00", 2) == 0) {
        qrzl_info("SELECT MF (3F00) detected, resetting context.");
        reset_vsim_context();
    }

    cJSON* command_context_node = NULL;
    cJSON* response_node = NULL;

    // 1. 鍦ㄥ綋鍓嶄笂涓嬫枃涓�鏌ユ壘
    if (vsim_context.current_node && (vsim_context.current_node->type & 255) == cJSON_Object) {
        response_node = cJSON_GetObjectItem(vsim_context.current_node, req_key);
        if (response_node) {
            qrzl_info("鍦ㄥ綋鍓嶄笂涓嬫枃涓�鎵惧埌鍛戒护: %s", vsim_context.current_path ? vsim_context.current_path : "root");
            command_context_node = vsim_context.current_node;
        }
    }

    // 2. 濡傛灉鍦ㄤ笂涓嬫枃涓�鎵句笉鍒帮紝鍒欎粠鏍圭洰褰曞紑濮嬫悳绱�
    if (!command_context_node) {
        cJSON* apdus_array = cJSON_GetObjectItem(apdu_root, "apdus");
        if (apdus_array) {
            int array_size = cJSON_GetArraySize(apdus_array);
            int i;
            for (i = 0; i < array_size; i++) {
                cJSON* apdu_item = cJSON_GetArrayItem(apdus_array, i);
                if (apdu_item && cJSON_GetObjectItem(apdu_item, req_key)) {
                    qrzl_info("鍦ㄦ牴 apdus 鏁扮粍涓�鎵惧埌鍛戒护");
                    command_context_node = apdu_item;
                    break;
                }
            }
        }
    }

    // 3. 澶勭悊鎵惧埌鐨勮妭鐐�
    if (command_context_node) {
        response_node = cJSON_GetObjectItem(command_context_node, req_key);
        if (handle_json_node(response_node, apdu_rsp, apdu_rsp_len) == 0) {
            qrzl_info("鎴愬姛澶勭悊鍛戒护: %s", req_key);
            print_hex("APDU RSP", apdu_rsp, *apdu_rsp_len);

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
            // 妫€鏌ユ槸鍚︿负閴存潈APDU锛屽�傛灉鏄�鍒欏彂閫佸埌鏈嶅姟鍣�
            if (is_auth_apdu(apdu_req, apdu_req_len)) {
                qrzl_info("妫€娴嬪埌閴存潈APDU锛屽噯澶囧彂閫佸埌鏈嶅姟鍣ㄨ繘琛岄壌鏉�");
                if (send_auth_apdu_to_server(apdu_req, apdu_req_len, apdu_rsp, apdu_rsp_len, slot) != 0) {
                    qrzl_warn("閴存潈APDU鍙戦€佸埌鏈嶅姟鍣ㄥけ璐ワ紝浣嗙户缁�澶勭悊鏈�鍦板搷搴�");
                }
            }
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

            // 鍦� SELECT 鍛戒护涓婃洿鏂颁笂涓嬫枃
            if (apdu_req[0] == 0x00 && apdu_req[1] == 0xA4) {
                if (vsim_context.current_path) {
                    free(vsim_context.current_path);
                }
                vsim_context.current_path = strdup(req_key);
                vsim_context.current_node = command_context_node;
                qrzl_info("涓婁笅鏂囧凡鏇存柊涓�: %s", req_key);
            }
            return 0;
        } else {
            qrzl_err("澶勭悊鍛戒护鐨� JSON 鑺傜偣澶辫触: %s", req_key);
        }
    }

    // 4. Default response if command not found
    qrzl_warn("APDU command not found in JSON table: %s", req_key);

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
    // 鍗充娇鍦↗SON琛ㄤ腑鎵句笉鍒帮紝涔熻�佹�€鏌ユ槸鍚︿负閴存潈APDU
    if (is_auth_apdu(apdu_req, apdu_req_len)) {
        qrzl_info("妫€娴嬪埌鏈�鐭ョ殑閴存潈APDU锛屽彂閫佸埌鏈嶅姟鍣ㄨ繘琛岄壌鏉�");
        // 涓烘湭鐭ラ壌鏉傾PDU鍒涘缓榛樿�ゅ搷搴�
        if (*apdu_rsp_len >= 2) {
            apdu_rsp[0] = 0x90;  // 榛樿�ゆ垚鍔熷搷搴�
            apdu_rsp[1] = 0x00;
            *apdu_rsp_len = 2;
        }
        // 鍙戦€佸埌鏈嶅姟鍣�
        if (send_auth_apdu_to_server(apdu_req, apdu_req_len, apdu_rsp, apdu_rsp_len, slot) != 0) {
            qrzl_warn("鏈�鐭ラ壌鏉傾PDU鍙戦€佸埌鏈嶅姟鍣ㄥけ璐�");
            // 濡傛灉鏈嶅姟鍣ㄩ€氫俊澶辫触锛岃繑鍥為敊璇�鍝嶅簲
            if (*apdu_rsp_len >= 2) {
                apdu_rsp[0] = 0x6F;  // 鎶€鏈�闂�棰�
                apdu_rsp[1] = 0x00;
                *apdu_rsp_len = 2;
            }
        }
        print_hex("APDU RSP (Auth)", apdu_rsp, *apdu_rsp_len);
        return 0;
    }
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

    // 5. 鏈�鎵惧埌鍛戒护鐨勯粯璁ゅ搷搴�
    if (*apdu_rsp_len >= 2) {
        // 鐗规畩澶勭悊: 鏈�鎵惧埌鐨� READ BINARY (00B2) 鍛戒护
        if (apdu_req_len == 5 && apdu_req[0] == 0x00 && apdu_req[1] == 0xB2) {
            uint8_t le = apdu_req[4];
            if (*apdu_rsp_len >= (uint16_t)le + 2) {
                memset(apdu_rsp, 0xFF, le);
                apdu_rsp[le]     = 0x90;
                apdu_rsp[le + 1] = 0x00;
                *apdu_rsp_len = le + 2;
                qrzl_info("Unhandled READ BINARY, returning %d bytes of FF + 9000.", le);
            } else {
                // 缂撳啿鍖轰笉瓒筹紝杩斿洖閿欒��
                apdu_rsp[0] = 0x6C; // SW1: Wrong length
                apdu_rsp[1] = 0x00; // SW2: No specific length given
                *apdu_rsp_len = 2;
            }
        } else {
            // 鍏朵粬鏈�鎵惧埌鐨勫懡浠わ紝杩斿洖 6A82
            apdu_rsp[0] = 0x6A; // SW1: Wrong parameter(s) P1-P2
            apdu_rsp[1] = 0x82; // SW2: File not found
            *apdu_rsp_len = 2;
        }

     
        print_hex("APDU RSP", apdu_rsp, *apdu_rsp_len);
        // 灏嗘渶缁堝搷搴旀墦鍗颁负瀛楃�︿覆
        char rsp_str[(*apdu_rsp_len) * 2 + 1];
        generate_apdu_key(apdu_rsp, *apdu_rsp_len, rsp_str);
        qrzl_info("APDU RSP (String): %s", rsp_str);
        
        return 0;
    }

    return -1;
#elif defined(JCV_FEATRUE_USE_JSON_VSIM_APDU_FROM_ALK_LOG)
    char req_key[512] = {0};
    generate_apdu_key(apdu_req, apdu_req_len, req_key);

    qrzl_info("===== [VSIM Slot %d] APDU Request =====", slot);
    print_hex("APDU REQ", apdu_req, apdu_req_len);
    qrzl_info("REQ KEY: %s", req_key);

    // 鐗规畩澶勭悊锛歋ELECT MF (3F00) -> 閲嶇疆涓婁笅鏂�
    if (apdu_req_len >= 7 && apdu_req[0] == 0x00 && apdu_req[1] == 0xA4 &&
        memcmp(apdu_req + 5, "\x3F\x00", 2) == 0) {
        qrzl_info("SELECT MF (3F00) detected, resetting context.");
        reset_vsim_context();
    }

    cJSON* target_node = NULL;
    cJSON* search_context = NULL;

    // 1. 鍦ㄥ綋鍓嶄笂涓嬫枃鐨� children 涓�鏌ユ壘
    if (vsim_context.current_node && (vsim_context.current_node->type & 255) == cJSON_Object) {
        search_context = cJSON_GetObjectItem(vsim_context.current_node, "children");
        if (search_context) {
            target_node = cJSON_GetObjectItem(search_context, req_key);
            if (target_node) {
                qrzl_info("Command found in current context's children: %s", vsim_context.current_path);
            }
        }
    }

    // 2. 濡傛灉鍦ㄤ笂涓嬫枃涓�鎵句笉鍒帮紝鍒欏湪鏍硅妭鐐规煡鎵�
    if (!target_node) {
        target_node = cJSON_GetObjectItem(apdu_root, req_key);
        if (target_node) {
            qrzl_info("Command found at root level.");
        } else {
            // 3. 濡傛灉鍦ㄦ牴涔熸壘涓嶅埌锛屽垯閬嶅巻鎵€鏈夋牴鏉＄洰鐨� children
            cJSON* root_entry = apdu_root->child;
            while(root_entry && !target_node) {
                cJSON* children = cJSON_GetObjectItem(root_entry, "children");
                if (children) {
                    target_node = cJSON_GetObjectItem(children, req_key);
                }
                root_entry = root_entry->next;
            }
            if (target_node) {
                qrzl_info("Command found in a root-level entry's children.");
            }
        }
    }

    // 3. 澶勭悊鎵惧埌鐨勮妭鐐�
    if (target_node) {
        if (handle_json_node(target_node, apdu_rsp, apdu_rsp_len) == 0) {
            qrzl_info("Successfully processed command: %s", req_key);
            print_hex("APDU RSP", apdu_rsp, *apdu_rsp_len);

            // 灏嗗搷搴旀墦鍗颁负瀛楃�︿覆
            char rsp_str[*apdu_rsp_len * 2 + 1];
            generate_apdu_key(apdu_rsp, *apdu_rsp_len, rsp_str);
            qrzl_info("APDU RSP (String): %s", rsp_str);

            // 4. 濡傛灉鏄� SELECT 鍛戒护骞朵笖鏈� children锛屽垯鏇存柊涓婁笅鏂�
            int is_select = (apdu_req[0] == 0x00 && apdu_req[1] == 0xA4);
            if (is_select && target_node && target_node->type == cJSON_Object && cJSON_GetObjectItem(target_node, "children")) {
                if (vsim_context.current_path) {
                    free(vsim_context.current_path);
                }
                vsim_context.current_path = strdup(req_key);
                vsim_context.current_node = target_node;
                qrzl_info("Context updated to: %s", req_key);
            }
            return 0;
        } else {
            qrzl_err("Failed to handle JSON node for command: %s", req_key);
        }
    }

    // 5. 鏈�鎵惧埌鍛戒护鐨勯粯璁ゅ搷搴�
    qrzl_warn("APDU command not found in JSON table: %s", req_key);
    if (*apdu_rsp_len >= 2) {
        // 鐗规畩澶勭悊: 鏈�鎵惧埌鐨� READ BINARY (00B2) 鍛戒护
        if (apdu_req_len == 5 && apdu_req[0] == 0x00 && apdu_req[1] == 0xB2) {
            uint8_t le = apdu_req[4];
            if (*apdu_rsp_len >= (uint16_t)le + 2) {
                memset(apdu_rsp, 0xFF, le);
                apdu_rsp[le]     = 0x90;
                apdu_rsp[le + 1] = 0x00;
                *apdu_rsp_len = le + 2;
                qrzl_info("Unhandled READ BINARY, returning %d bytes of FF + 9000.", le);
            } else {
                // 缂撳啿鍖轰笉瓒筹紝杩斿洖閿欒��
                apdu_rsp[0] = 0x6C; // SW1: Wrong length
                apdu_rsp[1] = 0x00; // SW2: No specific length given
                *apdu_rsp_len = 2;
            }
        } else {
            // 鍏朵粬鏈�鎵惧埌鐨勫懡浠わ紝杩斿洖 6A82
            apdu_rsp[0] = 0x6A; // SW1: Wrong parameter(s) P1-P2
            apdu_rsp[1] = 0x82; // SW2: File not found
            *apdu_rsp_len = 2;
        }

        // 灏嗘渶缁堝搷搴旀墦鍗颁负瀛楃�︿覆
        char rsp_str[(*apdu_rsp_len) * 2 + 1];
        generate_apdu_key(apdu_rsp, *apdu_rsp_len, rsp_str);
        qrzl_info("APDU RSP (String): %s", rsp_str);

        return 0;
    }

    return -1; // 琛ㄧず涓ラ噸閿欒��
#else
    char req_key[512] = {0};
    generate_apdu_key(apdu_req, apdu_req_len, req_key);

    qrzl_info("===== [VSIM Slot %d] APDU 澶勭悊寮€濮� =====", slot);
    print_hex("APDU REQ", apdu_req, apdu_req_len);
    qrzl_info("REQ KEY: %s", req_key);

    T_UICC_TRANSPORT_APDU_REQ_MSG *apdu_req_msg = (T_UICC_TRANSPORT_APDU_REQ_MSG *)malloc(sizeof(T_UICC_TRANSPORT_APDU_REQ_MSG));
    if (!apdu_req_msg) {
        qrzl_err("APDU 璇锋眰鏍煎紡閿欒��鎴栭暱搴︿笉瓒�");
        return -1;
    }

    // 鏍规嵁APDU闀垮害鍜屽唴瀹瑰姩鎬佽�剧疆command_case
    memset(apdu_req_msg, 0, sizeof(T_UICC_TRANSPORT_APDU_REQ_MSG));
    apdu_req_msg->card_selector = slot;
    apdu_req_msg->extended_length = (apdu_req_len > 256) ? TRUE : FALSE;
    
    // 瀹夊叏澶嶅埗APDU澶达紙鏈€澶�8瀛楄妭锛�
    uint16_t header_len = (apdu_req_len < sizeof(T_ZDrvUicc_ApduHeader)) ?
                          apdu_req_len : sizeof(T_ZDrvUicc_ApduHeader);
    memcpy(&apdu_req_msg->c_apdu, apdu_req, header_len);
    
    // 濡傛灉璇锋眰闀垮害涓嶈冻8瀛楄妭锛岃�板綍璀﹀憡
    if (apdu_req_len < sizeof(T_ZDrvUicc_ApduHeader)) {
        qrzl_warn("APDU璇锋眰闀垮害涓嶈冻%d瀛楄妭锛屽疄闄呴暱搴�:%d",
                 sizeof(T_ZDrvUicc_ApduHeader), apdu_req_len);
    }
    
    // 鏍规嵁APDU闀垮害纭�瀹氬懡浠ょ被鍨�
    if (apdu_req_len == 4) {
        // Case 1: 鍙�鏈夋寚浠ゅご
        apdu_req_msg->command_case = DRV_UICC_CMD_CASE_1;
    } else if (apdu_req_len == 5) {
        // Case 2: 鎸囦护澶� + Le
        apdu_req_msg->command_case = DRV_UICC_CMD_CASE_2;
        apdu_req_msg->c_apdu.le = apdu_req[4]; // 璁剧疆Le鍊�
    } else if (apdu_req_len > 5) {
        uint8_t lc = apdu_req[4]; // Lc浣嶇疆
        
        if (apdu_req_len == 5 + lc) {
            // Case 3: 鎸囦护澶� + Lc + 鏁版嵁
            apdu_req_msg->command_case = DRV_UICC_CMD_CASE_3;
            memcpy(apdu_req_msg->data_req, apdu_req + 5, lc);
        } else if (apdu_req_len == 6 + lc) {
            // Case 4: 鎸囦护澶� + Lc + 鏁版嵁 + Le
            apdu_req_msg->command_case = DRV_UICC_CMD_CASE_4;
            memcpy(apdu_req_msg->data_req, apdu_req + 5, lc);
            apdu_req_msg->c_apdu.le = apdu_req[5 + lc]; // 璁剧疆Le鍊�
        } else {
            qrzl_err("鏃犳晥鐨凙PDU闀垮害: %d, Lc=%d", apdu_req_len, lc);
            free(apdu_req_msg);
            return -1;
        }
    } else {
        qrzl_err("鏃犳晥鐨凙PDU闀垮害: %d (鏈€灏忓€�4瀛楄妭)", apdu_req_len);
        free(apdu_req_msg);
        return -1;
    }
    
    // 璁板綍APDU澶翠俊鎭�
    qrzl_info("APDU澶�: CLA=%02X, INS=%02X, P1=%02X, P2=%02X",
              apdu_req_msg->c_apdu.cla, apdu_req_msg->c_apdu.ins,
              apdu_req_msg->c_apdu.p1, apdu_req_msg->c_apdu.p2);
    // 鍒涘缓鍝嶅簲娑堟伅
    T_ZDrvUicc_ApduHeader tCApdu;
	T_UICC_TRANSPORT_APDU_RSP_MSG *apdu_rsp_msg = (T_UICC_TRANSPORT_APDU_RSP_MSG *)malloc(sizeof(T_UICC_TRANSPORT_APDU_RSP_MSG));

    apdu_rsp_msg->card_selector = slot;
	memcpy(&tCApdu, &apdu_req_msg->c_apdu, sizeof(T_ZDrvUicc_ApduHeader));
	// 鏍规嵁鍛戒护绫诲瀷浼犻€掓�ｇ‘鐨勬暟鎹�闀垮害
	uint16_t data_len = 0;
	if (apdu_req_msg->command_case == DRV_UICC_CMD_CASE_3 ||
	    apdu_req_msg->command_case == DRV_UICC_CMD_CASE_4) {
		data_len = apdu_req[4]; // Lc鍊�
	}
	
	apdu_rsp_msg->func_resp = zVcard_TransportApdu(
		apdu_req_msg->card_selector,
		apdu_req_msg->command_case,
		apdu_req_msg->extended_length,
		tCApdu,
		&apdu_rsp_msg->r_apdu_ptr,
		apdu_req_msg->data_req
	);
    // 璋冪敤 zVcard_TransportApdu apdu_req_msg->data_req 杩涘幓鍚庯紝rsp 鏀惧埌杩欓噷鏀瑰啓浜�(浣滀负浼犲叆浼犲嚭buffer)
    // 澶嶅埗鍝嶅簲鏁版嵁鍒� apdu_rsp_msg
	memcpy(apdu_rsp_msg->data_rsp, apdu_req_msg->data_req, apdu_rsp_msg->r_apdu_ptr.luicc); // 杩欒�屼唬鐮佷技涔庝笉姝ｇ‘锛屽簲璇ユ槸浠巖_apdu_ptr澶嶅埗鏁版嵁

    qrzl_info("func_resp %d r_apdu_ptr.luicc %d *apdu_rsp_len %d\n", apdu_rsp_msg->func_resp,
              apdu_rsp_msg->r_apdu_ptr.luicc, *apdu_rsp_len);
    // 妫€鏌ュ嚱鏁拌皟鐢ㄦ槸鍚︽垚鍔�
    if (apdu_rsp_msg->func_resp == 0) { // 鍋囪�� 0 琛ㄧず鎴愬姛
        uint16_t total_rsp_len = 0; // 鎬诲搷搴旈暱搴︼紙鏁版嵁+鐘舵€佸瓧锛�
        
        // 澶嶅埗鏁版嵁閮ㄥ垎
        if (apdu_rsp_msg->r_apdu_ptr.luicc > 0) {
            uint16_t data_copy_len = apdu_rsp_msg->r_apdu_ptr.luicc;
            // 璁＄畻鍓╀綑绌洪棿锛堟€荤紦鍐插尯澶у皬鍑忓幓鐘舵€佸瓧鎵€闇€鐨�2瀛楄妭锛�
            uint16_t max_data_len = (*apdu_rsp_len >= 2) ? (*apdu_rsp_len - 2) : 0;
            
            if (data_copy_len > max_data_len) {
                data_copy_len = max_data_len;
                qrzl_warn("鍝嶅簲鏁版嵁闀垮害瓒呰繃缂撳啿鍖哄ぇ灏忥紙鑰冭檻鐘舵€佸瓧鍚庯級锛屾埅鏂�鍒� %d 瀛楄妭", data_copy_len);
            }
            
            memcpy(apdu_rsp, apdu_rsp_msg->data_rsp, data_copy_len);
            total_rsp_len = data_copy_len;
        }
        
        // 娣诲姞鐘舵€佸瓧 SW1 鍜� SW2
        if (*apdu_rsp_len >= total_rsp_len + 2) {
            apdu_rsp[total_rsp_len] = apdu_rsp_msg->r_apdu_ptr.sw1;
            apdu_rsp[total_rsp_len + 1] = apdu_rsp_msg->r_apdu_ptr.sw2;
            total_rsp_len += 2;
        } else if (*apdu_rsp_len == total_rsp_len + 1) {
            // 鍙�鑳芥斁涓€涓�瀛楄妭
            apdu_rsp[total_rsp_len] = apdu_rsp_msg->r_apdu_ptr.sw1;
            total_rsp_len += 1;
            qrzl_warn("鐘舵€佸瓧SW2鍥犵紦鍐插尯涓嶈冻琚�涓㈠純");
        } else {
            qrzl_warn("鐘舵€佸瓧鍥犵紦鍐插尯涓嶈冻琚�涓㈠純");
        }
        
        *apdu_rsp_len = total_rsp_len;
        
        if (total_rsp_len > 0) {
            print_hex("APDU RSP", apdu_rsp, total_rsp_len);
            qrzl_debug("鍝嶅簲璇︽儏: 鏁版嵁闀垮害=%d, 鐘舵€佸瓧=%02X%02X",
                      apdu_rsp_msg->r_apdu_ptr.luicc,
                      apdu_rsp_msg->r_apdu_ptr.sw1,
                      apdu_rsp_msg->r_apdu_ptr.sw2);
        } else {
            qrzl_err("zVcard_TransportApdu 鎴愬姛浣嗘病鏈夊搷搴旀暟鎹�");
        }
    } else {
        qrzl_err("zVcard_TransportApdu 璋冪敤澶辫触, 閿欒��鐮�: %d", apdu_rsp_msg->func_resp);
        // 鏍规嵁閿欒��鐮佽�剧疆鍝嶅簲鐘舵€佸瓧 (SW1, SW2)
        apdu_rsp[0] = 0x6F; // SW1: Technical problem
        apdu_rsp[1] = 0x00; // SW2: No specific diagnosis
        *apdu_rsp_len = 2;
        // 鎵撳嵃閿欒��鍝嶅簲
        print_hex("APDU RSP (閿欒��)", apdu_rsp, *apdu_rsp_len);
    }

    // 閲婃斁鍐呭瓨
    free(apdu_req_msg);
    free(apdu_rsp_msg);

    qrzl_info("===== [VSIM Slot %d] APDU 澶勭悊瀹屾垚 =====", slot);
    return 0; // 杩斿洖0琛ㄧず澶勭悊瀹屾垚
#endif
}

// ====================== 澶栭儴鍛戒护澶勭悊鍑芥暟 ======================

/**
 * 澶勭悊澶栭儴 CFUN 鍛戒护
 * 璇诲彇 /etc_rw/cfun 鏂囦欢涓�鐨勫€硷紝璋冪敤鐩稿簲鐨� CFUN 鎺ュ彛
 * 杩斿洖鍊�: 0-鎴愬姛, -1-澶辫触
 */
static int process_external_cfun_command(void) {
    FILE *fp = NULL;
    char cfun_value_str[16] = {0};
    int cfun_value = -1;
    int ret = 0;

    // 灏濊瘯鎵撳紑 /etc_rw/cfun 鏂囦欢
    fp = fopen("/etc_rw/cfun", "r");
    if (fp == NULL) {
        // 鏂囦欢涓嶅瓨鍦ㄦ垨鏃犳硶鎵撳紑锛岃繖鏄�姝ｅ父鎯呭喌锛屼笉璁板綍閿欒��
        return 0;
    }

    // 璇诲彇鏂囦欢鍐呭��
    if (fgets(cfun_value_str, sizeof(cfun_value_str), fp) == NULL) {
        qrzl_err("璇诲彇 /etc_rw/cfun 鏂囦欢鍐呭�瑰け璐�");
        fclose(fp);
        return -1;
    }

    fclose(fp);

    // 鍘婚櫎鎹㈣�岀�﹀拰绌虹櫧瀛楃��
    char *p = cfun_value_str;
    while (*p && (*p == ' ' || *p == '\t' || *p == '\n' || *p == '\r')) {
        p++;
    }

    // 杞�鎹�涓烘暣鏁�
    cfun_value = atoi(p);

    // 楠岃瘉 CFUN 鍊肩殑鏈夋晥鎬� (閫氬父涓� 0-4)
    if (cfun_value < 0 || cfun_value > 4) {
        qrzl_err("鏃犳晥鐨� CFUN 鍊�: %d", cfun_value);
        return -1;
    }

    qrzl_info("妫€娴嬪埌澶栭儴 CFUN 鍛戒护: %d", cfun_value);

    // 璋冪敤 fibo_set_cfun_mode 璁剧疆 CFUN 妯″紡
    ret = fibo_set_cfun_mode((uint8_t)cfun_value);
    if (ret != 0) {
        qrzl_err("璁剧疆 CFUN 妯″紡澶辫触, 閿欒��鐮�: %d", ret);
        return -1;
    }

    qrzl_info("鎴愬姛璁剧疆 CFUN 妯″紡: %d", cfun_value);

    // 绛夊緟涓€娈垫椂闂磋�╄�剧疆鐢熸晥
    sleep(2);

    // 鏌ヨ�㈠綋鍓� CFUN 妯″紡杩涜�岄獙璇�
    uint8_t current_cfun = 0;
    ret = fibo_get_cfun_mode(&current_cfun);
    if (ret == 0) {
        qrzl_info("褰撳墠 CFUN 妯″紡鏌ヨ�㈢粨鏋�: %d", current_cfun);

        // 楠岃瘉璁剧疆鏄�鍚︽垚鍔�
        if (current_cfun == (uint8_t)cfun_value) {
            qrzl_info("CFUN 妯″紡璁剧疆楠岃瘉鎴愬姛");
        } else {
            qrzl_warn("CFUN 妯″紡璁剧疆楠岃瘉澶辫触: 鏈熸湜=%d, 瀹為檯=%d", cfun_value, current_cfun);
        }
    } else {
        qrzl_err("鏌ヨ�㈠綋鍓� CFUN 妯″紡澶辫触, 閿欒��鐮�: %d", ret);
    }

    // 鍒犻櫎鍛戒护鏂囦欢锛岄伩鍏嶉噸澶嶆墽琛�
    if (unlink("/etc_rw/cfun") != 0) {
        qrzl_warn("鍒犻櫎 /etc_rw/cfun 鏂囦欢澶辫触");
    } else {
        qrzl_info("宸插垹闄� /etc_rw/cfun 鍛戒护鏂囦欢");
    }

    return 0;
}

// ====================== 绾跨▼鍑芥暟 ======================

static void* qrzl_vsim_thread_handler(void* arg) {
    qrzl_info("VSIM 鐩戞帶绾跨▼鍚�鍔�");

    while (1) {
        sleep(5);

        // 澶勭悊澶栭儴 CFUN 鍛戒护
        process_external_cfun_command();

         // 鏌ヨ�� IMEI
        uint8_t imei[16] = {0};
        if (fibo_get_IMEI_by_simid(imei, sizeof(imei), 1) == 0) {
            qrzl_info("SIM1 IMEI: %s", imei);

            // IMEI鏍煎紡楠岃瘉鍜屽垎鏋�
            const char* imei_str = (const char*)imei;
            int imei_len = strlen(imei_str);

            if (imei_len == 15) {
                // 鏍囧噯IMEI闀垮害涓�15浣�
                qrzl_info("SIM1 IMEI鏍煎紡: 鏍囧噯15浣�");

                // 鎻愬彇IMEI缁勬垚閮ㄥ垎
                char tac[9] = {0};      // Type Allocation Code (鍓�8浣�)
                char snr[7] = {0};      // Serial Number (涓�闂�6浣�)
                char cd = imei_str[14]; // Check Digit (鏈€鍚�1浣�)

                strncpy(tac, imei_str, 8);
                strncpy(snr, imei_str + 8, 6);

                qrzl_info("SIM1 IMEI鍒嗘瀽: TAC=%s, SNR=%s, CD=%c", tac, snr, cd);
            } else if (imei_len == 14) {
                qrzl_info("SIM1 IMEI鏍煎紡: 14浣嶏紙鏃犳牎楠屼綅锛�");
            } else {
                qrzl_warn("SIM1 IMEI鏍煎紡寮傚父: 闀垮害=%d", imei_len);
            }
        } else {
            qrzl_err("鑾峰彇 SIM1 IMEI 澶辫触");
        }
        
        // 瀹氭湡妫€鏌� SIM 鐘舵€�
        E_SIM_STATUS sim_status;
        if (fibo_get_SIM_Status_by_simid(&sim_status, 1) == 0) {
            const char* status_str = "鏈�鐭�";
            switch (sim_status) {
                case E_SIM_STAT_READY: status_str = "灏辩华"; break;
                case E_SIM_STAT_PIN: status_str = "闇€瑕丳IN"; break;
                case E_SIM_STAT_PUK: status_str = "闇€瑕丳UK"; break;
                case E_SIM_STAT_NOT_INSERTED: status_str = "鏈�鎻掑叆"; break;
                case E_SIM_STAT_BUSY: status_str = "蹇�"; break;
            }
            qrzl_info("SIM1 鐘舵€�: %s", status_str);
        }
        
        // 鏌ヨ�� ICCID
        uint8_t iccid[21] = {0};
        if (fibo_get_CCID_by_simid(iccid, sizeof(iccid), 1) == 0) {
            qrzl_info("SIM1 ICCID: %s", iccid);
        } else {
            qrzl_err("鑾峰彇 SIM1 ICCID 澶辫触");
        }

        // 鏌ヨ��mcc mnc (imsi)
        char mcc[4] = {0};
        char mnc[4] = {0};
        if (fibo_get_MCC_MNC_by_simid(mcc, mnc, 1) == 0) {
            qrzl_info("SIM1: mcc %s mnc %s", mcc, mnc);
        } else {
            qrzl_err("鑾峰彇 SIM1 mcc mnc 澶辫触");
        }

        // 鏌ヨ��淇″彿寮哄害 (CSQ)
        int32_t rssi = 0;
        int32_t ber = 0;
        if (fibo_get_CSQ_by_simid(&rssi, &ber, 1) == 0) {
            // rssi鍊艰寖鍥�: 0-31 (0琛ㄧず-113dBm鎴栨洿浣�, 31琛ㄧず-51dBm鎴栨洿楂�)
            // ber鍊艰寖鍥�: 0-7 (璇�鐮佺巼锛�0琛ㄧず鏈€濂斤紝7琛ㄧず鏈€宸�)

            // 杞�鎹�涓篸Bm: dBm = -113 + (rssi * 2)
            int rssi_dbm = -113 + (rssi * 2);
            qrzl_info("SIM1 淇″彿寮哄害: RSSI=%d, BER=%d, RSSI_dBm=%ddBm", rssi, ber, rssi_dbm);

            // 淇″彿璐ㄩ噺璇勪及锛堝熀浜嶳SSI鍊硷級
            const char* signal_quality;
            if (rssi >= 20) {
                signal_quality = "浼樼�€";
            } else if (rssi >= 15) {
                signal_quality = "鑹�濂�";
            } else if (rssi >= 10) {
                signal_quality = "涓€鑸�";
            } else if (rssi >= 5) {
                signal_quality = "杈冨樊";
            } else if (rssi == 99) {
                signal_quality = "鏈�鐭�";
            } else {
                signal_quality = "寰堝樊";
            }

            // 璇�鐮佺巼璇勪及
            const char* ber_quality;
            if (ber == 99) {
                ber_quality = "鏈�鐭�";
            } else if (ber <= 1) {
                ber_quality = "浼樼�€";
            } else if (ber <= 3) {
                ber_quality = "鑹�濂�";
            } else if (ber <= 5) {
                ber_quality = "涓€鑸�";
            } else {
                ber_quality = "杈冨樊";
            }

            qrzl_info("SIM1 淇″彿璐ㄩ噺: %s, 璇�鐮佺巼: %s", signal_quality, ber_quality);
        } else {
            qrzl_err("鑾峰彇 SIM1 淇″彿寮哄害澶辫触");
        }

         // 瀹氭湡鑾峰彇缃戠粶淇℃伅
        RegInfo_t reg_info;
        if (fibo_get_RegInfo_by_simid(&reg_info, 1) == 0) {
            qrzl_info("缃戠粶鐘舵€�: RAT=%d, 鏈嶅姟鐘舵€�=%d",
                      reg_info.curr_rat, reg_info.nStatus);
        }


        // 璁板綍褰撳墠涓婁笅鏂�
        qrzl_info("褰撳墠涓婁笅鏂�: %s",
                  vsim_context.current_path ? vsim_context.current_path : "鏃�");
    }
    
    return NULL;
}

void start_vsim_thread() {
    pthread_t qrzl_vsim_tid;
    int err = pthread_create(&qrzl_vsim_tid, NULL, 
                            qrzl_vsim_thread_handler, NULL);
    if (err != 0) {
        qrzl_err("鍒涘缓 VSIM 绾跨▼澶辫触, 閿欒��鐮�: %d", err);
    } else {
        qrzl_info("VSIM 鐩戞帶绾跨▼鍒涘缓鎴愬姛");
    }
}

// ====================== JSON 鍔犺浇鍑芥暟 ======================

static int load_apdu_table(const char* filename) {
    FILE* fp = fopen(filename, "r");
    if (!fp) {
        qrzl_err("鏃犳硶鎵撳紑 APDU 鏂囦欢: %s", filename);
        return -1;
    }

    // 鑾峰彇鏂囦欢澶у皬
    fseek(fp, 0, SEEK_END);
    long fsize = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    
    // 鍒嗛厤鍐呭瓨
    char* json_str = malloc(fsize + 1);
    if (!json_str) {
        fclose(fp);
        qrzl_err("鍒嗛厤鍐呭瓨澶辫触");
        return -1;
    }
    
    // 璇诲彇鏂囦欢鍐呭��
    size_t bytes_read = fread(json_str, 1, fsize, fp);
    fclose(fp);
    
    if (bytes_read != fsize) {
        qrzl_err("璇诲彇鏂囦欢澶辫触: %s (鏈熸湜 %ld, 瀹為檯 %zu)", 
                 filename, fsize, bytes_read);
        free(json_str);
        return -1;
    }
    
    json_str[fsize] = '\0';
    
    // 妫€娴嬪苟璺宠繃 UTF-8 BOM
    char* json_start = json_str;
    if (fsize >= 3 && 
        (uint8_t)json_str[0] == 0xEF && 
        (uint8_t)json_str[1] == 0xBB && 
        (uint8_t)json_str[2] == 0xBF) {
        qrzl_info("妫€娴嬪埌 UTF-8 BOM锛岃烦杩囧墠3瀛楄妭");
        json_start += 3;
    }

    // 瑙ｆ瀽 JSON
    apdu_root = cJSON_Parse(json_start);
    if (!apdu_root) {
        const char* error_ptr = cJSON_GetErrorPtr();
        qrzl_err("JSON 瑙ｆ瀽澶辫触: %s", error_ptr);
        
        // 杈撳嚭閿欒��浣嶇疆闄勮繎鐨勪笂涓嬫枃
        if (error_ptr) {
            const char* start = error_ptr - 30;
            if (start < json_start) start = json_start;
            
            const char* end = error_ptr + 30;
            if (end > json_start + fsize) end = json_start + fsize;
            
            size_t context_len = end - start;
            char context[context_len + 1];
            memcpy(context, start, context_len);
            context[context_len] = '\0';
            
            qrzl_err("瑙ｆ瀽閿欒��浣嶇疆: %.*s", (int)context_len, context);
        }
        
        // 杈撳嚭鏂囦欢鍓�100瀛楄妭鐨勫崄鍏�杩涘埗
        size_t debug_len = fsize > 100 ? 100 : fsize;
        char hex_buf[debug_len * 3 + 1];
        char *p = hex_buf;
        size_t i;
        for (i = 0; i < debug_len; i++) {
            p += sprintf(p, "%02X ", (unsigned char)json_str[i]);
        }
        *p = '\0';
        qrzl_err("JSON 鏂囦欢鍓� %zu 瀛楄妭 (鍗佸叚杩涘埗): %s", debug_len, hex_buf);
        
        free(json_str);
        return -1;
    }
    
    // 鍒濆�嬪寲涓婁笅鏂�
    reset_vsim_context();
    
    free(json_str);
    qrzl_info("APDU 琛ㄥ姞杞芥垚鍔�: %s", filename);
    return 0;
}

// ====================== 浜嬩欢澶勭悊鍑芥暟 ======================
static int32_t vsim_event_handler(E_MODEM_EVENT_ID event_id, void* ind_data, uint32_t ind_data_len) {
    qrzl_info("===== 鏀跺埌 Modem 浜嬩欢閫氱煡 =====");
    qrzl_info("浜嬩欢 ID: 0x%02X (%d)", event_id, event_id);
    qrzl_info("鏁版嵁闀垮害: %d 瀛楄妭", ind_data_len);
    
    switch (event_id) {
        case E_RF_RESOURCE_CALLBACK_EVENT:
            qrzl_info("浜嬩欢绫诲瀷: 灏勯�戣祫婧愬垏鎹�");
            break;
            
        case E_NW_ATTACH_COMMPLETE_EVENT:
            qrzl_info("浜嬩欢绫诲瀷: 缃戠粶闄勭潃瀹屾垚");
            if (ind_data_len == 1) {
                uint8_t sim_id = *(uint8_t*)ind_data;
                qrzl_info("SIM 妲戒綅: %d 宸叉垚鍔熼檮鐫€缃戠粶", sim_id);
            }
            break;
            
        case E_DATA_PDN_ACT_SUCC_EVENT:
            qrzl_info("浜嬩欢绫诲瀷: PDN 婵€娲诲畬鎴�");
            if (ind_data_len > 0) {
                print_hex("PDN 婵€娲绘暟鎹�", (uint8_t*)ind_data, ind_data_len);
            }
            break;
            
        case E_GET_CELLINFO_BY_SIMID_EVENT:
            qrzl_info("浜嬩欢绫诲瀷: 灏忓尯淇℃伅鑾峰彇瀹屾垚");
            if (ind_data_len == sizeof(CellInfo_t)) {
                CellInfo_t *cell_info = (CellInfo_t*)ind_data;
                qrzl_info("褰撳墠 RAT: %d", cell_info->curr_rat);
                qrzl_info("鏈嶅姟鐘舵€�: %d", cell_info->service_status);
            }
            break;
            
        default:
            qrzl_info("浜嬩欢绫诲瀷: 鏈�鐭ヤ簨浠�");
            if (ind_data_len > 0) {
                print_hex("浜嬩欢鏁版嵁", (uint8_t*)ind_data, ind_data_len);
            }
            break;
    }
    
    qrzl_info("==============================");
    return 0;
}

// ====================== VSIM 鏈嶅姟鍒濆�嬪寲 ======================

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
// 鍒濆�嬪寲HTTP閰嶇疆
static int initialize_http_config(void) {
    // 鍒濆�嬪寲curl鍏ㄥ眬鐜�澧�
    CURLcode curl_ret = curl_global_init(CURL_GLOBAL_DEFAULT);
    if (curl_ret != CURLE_OK) {
        qrzl_err("curl鍏ㄥ眬鍒濆�嬪寲澶辫触: %s", curl_easy_strerror(curl_ret));
        return -1;
    }

    // 浠庨厤缃�鏂囦欢鎴栫幆澧冨彉閲忚�诲彇鏈嶅姟鍣ㄥ湴鍧€
    char config_url[256] = {0};
    // 杩欓噷鍙�浠ヤ粠閰嶇疆鏂囦欢璇诲彇锛屾殏鏃朵娇鐢ㄩ粯璁ゅ€�
    // 鍙�浠ラ€氳繃鐜�澧冨彉閲廣SIM_AUTH_SERVER_URL鏉ヨ�嗙洊
    char* env_auth_url = getenv("VSIM_AUTH_SERVER_URL");
    if (env_auth_url && strlen(env_auth_url) > 0) {
        strncpy(auth_server_url, env_auth_url, sizeof(auth_server_url) - 1);
        qrzl_info("浣跨敤鐜�澧冨彉閲忚�剧疆鐨勯壌鏉冩湇鍔″櫒鍦板潃: %s", auth_server_url);
    } else {
        qrzl_info("浣跨敤榛樿�ら壌鏉冩湇鍔″櫒鍦板潃: %s", auth_server_url);
    }

    // 閰嶇疆鐧诲綍鏈嶅姟鍣ㄥ湴鍧€
    char* env_login_url = getenv("VSIM_LOGIN_SERVER_URL");
    if (env_login_url && strlen(env_login_url) > 0) {
        strncpy(login_server_url, env_login_url, sizeof(login_server_url) - 1);
        qrzl_info("浣跨敤鐜�澧冨彉閲忚�剧疆鐨勭櫥褰曟湇鍔″櫒鍦板潃: %s", login_server_url);
    } else {
        qrzl_info("浣跨敤榛樿�ょ櫥褰曟湇鍔″櫒鍦板潃: %s", login_server_url);
    }

    // 浠庣幆澧冨彉閲忚�诲彇瓒呮椂閰嶇疆
    char* env_timeout = getenv("VSIM_HTTP_TIMEOUT");
    if (env_timeout) {
        int timeout = atoi(env_timeout);
        if (timeout > 0 && timeout <= 300) {
            http_timeout = timeout;
        }
    }
    qrzl_info("HTTP璇锋眰瓒呮椂璁剧疆: %d绉�", http_timeout);

    // 浠庣幆澧冨彉閲忚�诲彇閲嶈瘯娆℃暟閰嶇疆
    char* env_retry = getenv("VSIM_MAX_RETRY");
    if (env_retry) {
        int retry = atoi(env_retry);
        if (retry >= 0 && retry <= 10) {
            max_retry_count = retry;
        }
    }
    qrzl_info("HTTP璇锋眰鏈€澶ч噸璇曟�℃暟: %d", max_retry_count);

    return 0;
}
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

static int initialize_vsim_service(void) {
    int ret;

    qrzl_info("鍒濆�嬪寲 VSIM 鏈嶅姟...");

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
    // 0. 鍒濆�嬪寲HTTP閰嶇疆
    if (initialize_http_config() != 0) {
        qrzl_err("HTTP閰嶇疆鍒濆�嬪寲澶辫触");
        return -1;
    }

    // 1. 璁惧�囩櫥褰曡幏鍙朣IM鍗′俊鎭�
    if (device_login_and_get_sim_info() != 0) {
        qrzl_err("璁惧�囩櫥褰曞け璐ワ紝浣跨敤榛樿�ら厤缃�");
        // 浣跨敤榛樿�ら厤缃�
        char real_imsi[16] = {0};  
        strcpy(device_info.imei, "867554011112811");
        strcpy(device_info.imsi, "084906404941107221");
        strcpy(device_info.iccid, "9868404791520C407221");
        strcpy(device_info.device_id, "33332513000045");
        convert_sim_imsi_to_real(device_info.imsi, real_imsi, sizeof(real_imsi));
        device_info.operator = identify_operator_by_imsi(real_imsi);
        device_info.is_valid = 1;
        qrzl_info("浣跨敤榛樿��SIM鍗′俊鎭�锛岃繍钀ュ晢: %s", get_operator_name(device_info.operator));
    }

    // 2. 璁剧疆浠庢湇鍔″櫒鑾峰彇鐨処MEI
    ret = fibo_set_IMEI_by_simid((uint8_t*)device_info.imei, strlen(device_info.imei), 1);
    if (ret != 0) {
        qrzl_err("璁剧疆 IMEI 澶辫触, 閿欒��鐮�: %d", ret);
    } else {
        qrzl_info("鎴愬姛璁剧疆 SIM1 IMEI: %s", device_info.imei);
    }
#else
    // 1. 璁剧疆 IMEI
    const char* imei = "862769025435956";
    ret = fibo_set_IMEI_by_simid((uint8_t*)imei, strlen(imei), 1);
    if (ret != 0) {
        qrzl_err("璁剧疆 IMEI 澶辫触, 閿欒��鐮�: %d", ret);
    } else {
        qrzl_info("鎴愬姛璁剧疆 SIM1 IMEI: %s", imei);
    }
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM

    // 3. 鏍规嵁杩愯惀鍟嗙被鍨嬪姩鎬佸姞杞� APDU 琛�
#ifdef JCV_FEATRUE_USE_JSON_VSIM_APDU_FROM_ALK_ROOTFS
#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
    const char* json_file = get_operator_json_file(device_info.operator);
    qrzl_info("鏍规嵁杩愯惀鍟�(%s)鍔犺浇APDU琛�: %s", get_operator_name(device_info.operator), json_file);
    if (load_apdu_table(json_file) != 0) {
        qrzl_err("APDU 琛ㄥ姞杞藉け璐�: %s", json_file);
        // 灏濊瘯鍔犺浇榛樿�ょ殑绉诲姩JSON鏂囦欢浣滀负澶囩敤
        if (device_info.operator != OPERATOR_CMCC) {
            qrzl_warn("灏濊瘯鍔犺浇榛樿�ょЩ鍔ˋPDU琛ㄤ綔涓哄�囩敤");
            if (load_apdu_table("/etc/cmcc.json") != 0) {
                qrzl_err("澶囩敤APDU琛ㄤ篃鍔犺浇澶辫触");
                return -1;
            }
        } else {
            return -1;
        }
    }
#else
    if (load_apdu_table("/etc/cmcc.json") != 0) {
        qrzl_err("APDU 琛ㄥ姞杞藉け璐�");
        return -1;
    }
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM
#else
    if (load_apdu_table("/etc/vsim_apdu.json") != 0) {
        qrzl_err("APDU 琛ㄥ姞杞藉け璐�");
        return -1;
    }
#endif

    // 4. 娉ㄥ唽浜嬩欢鍥炶皟
    ret = fibo_mdm_event_regist(vsim_event_handler);
    if (ret != 0) {
        qrzl_err("娉ㄥ唽浜嬩欢鍥炶皟澶辫触, 閿欒��鐮�: %d", ret);
    } else {
        qrzl_info("Modem 浜嬩欢鍥炶皟娉ㄥ唽鎴愬姛");
    }

    // 5. 鍒濆�嬪寲 VSIM
#ifdef JCV_FEATRUE_USE_VSIM_ENGINE
    zVcard_Init();
#endif
    fibo_vsim_init(vsim_apdu_handler, 1);
    qrzl_info("VSIM 鍒濆�嬪寲鎴愬姛");

    // 6. 楠岃瘉 IMEI 璁剧疆
    uint8_t actual_imei[16] = {0};
    ret = fibo_get_IMEI_by_simid(actual_imei, sizeof(actual_imei), 1);
    if (ret == 0) {
        qrzl_info("IMEI 楠岃瘉鎴愬姛: %s", actual_imei);
    } else {
        qrzl_err("IMEI 楠岃瘉澶辫触, 閿欒��鐮�: %d", ret);
    }

    return 0;
}

// ====================== 璧勬簮娓呯悊 ======================

void cleanup_vsim_resources() {
    qrzl_info("娓呯悊 VSIM 璧勬簮...");

    // 娓呯悊 JSON 璧勬簮
    if (apdu_root) {
        cJSON_Delete(apdu_root);
        apdu_root = NULL;
    }

    // 娓呯悊涓婁笅鏂�
    reset_vsim_context();

    // 娓呯悊鑷�瀹氫箟 IMSI/ICCID
    if (custom_imsi) {
        free(custom_imsi);
        custom_imsi = NULL;
    }
    if (custom_iccid) {
        free(custom_iccid);
        custom_iccid = NULL;
    }

#ifdef JCV_FEATRUE_ITO_CLOUD_VSIM
    // 娓呯悊curl鍏ㄥ眬鐜�澧�
    curl_global_cleanup();
    qrzl_info("HTTP璧勬簮娓呯悊瀹屾垚");
#endif // JCV_FEATRUE_ITO_CLOUD_VSIM
}

#ifndef JCV_FEATRUE_USE_VSIM_ENGINE
int check_vsim_bin(int fd)
{
#define VSIM_BIN_AUTH_LEN 520
	int fd_r = open("/etc_ro/vSim.bin",O_RDONLY);
	if(fd_r >= 0) {
		unsigned char buf_r[VSIM_BIN_AUTH_LEN] = {0};
		unsigned char buf[VSIM_BIN_AUTH_LEN] = {0};
		int read_len = 0;
		int read_len_r = 0;
again1:
		read_len = read(fd, buf, sizeof(buf));
		if (read_len < 0) {
			if (errno == EINTR){
				printf("@vsim@ Read bin, interrupted!\n");
				goto again1;
			}else{
				printf("@vsim@ Read bin fail, error:[%d]%s\n", errno, strerror(errno));
				goto err_out;
			}
		}else if(read_len != sizeof(buf)){
			printf("@vsim@ Read bin len=%d err\n", read_len);
			goto err_out;
		}
again2:
		read_len_r = read(fd_r, buf_r, sizeof(buf_r));
		if (read_len_r < 0) {
			if (errno == EINTR){
				printf("@vsim@ Read bin_ro, interrupted!\n");
				goto again2;
			}else{
				printf("@vsim@ Read bin_ro fail, error:[%d]%s\n", errno, strerror(errno));
				goto err_out;
			}
		}else if(read_len_r != sizeof(buf_r)){
			printf("@vsim@ Read bin_ro len=%d err\n", read_len_r);
			goto err_out;
		}
		close(fd_r);
		return memcmp(buf_r, buf, sizeof(buf_r));
	} else {
		printf("@vsim@ open vsim_ro fail, error:[%d]%s", errno, strerror(errno));
		return -1;
	}

err_out:
	close(fd_r);
	return -1;
}
#endif

// ====================== 涓诲嚱鏁� ======================

int main(void) {
    qrzl_info("======================================");
    qrzl_info("        VSIM 鏈嶅姟鍚�鍔ㄤ腑...");
    qrzl_info("======================================");

#ifndef JCV_FEATRUE_USE_VSIM_ENGINE
    int fd = open("/mnt/userdata/vSim.bin", O_RDONLY);
    printf("@vsim@ open userdata/vSim.bin fd: %d er_no: %d\n", fd, errno);
    if(fd >= 0)
    {
        if(check_vsim_bin(fd)){
            printf("@vsim@ check_vsim_bin fail copy form ro\n");
            system("cp -fp /etc_ro/vSim.bin /mnt/userdata/");
        }
        close(fd);
    }
    else {
        system("cp -fp /etc_ro/vSim.bin /mnt/userdata/");
    }
#endif

#ifdef JCV_FEATRUE_QRZl_VSIM_SERVICE  
    // 鍒濆�嬪寲 VSIM 鏈嶅姟
    if (initialize_vsim_service() != 0) {
        qrzl_err("VSIM 鏈嶅姟鍒濆�嬪寲澶辫触");
        cleanup_vsim_resources();
        return -1;
    }

    // 鍚�鍔ㄧ洃鎺х嚎绋�
    start_vsim_thread();
    qrzl_info("VSIM 鏈嶅姟宸叉垚鍔熷惎鍔�");
    qrzl_info("======================================");
#endif
    
    // 涓诲惊鐜�
    while (1) {
        sleep(10);
        qrzl_debug("涓诲惊鐜�杩愯�屼腑...");
    }
    
    // 娓呯悊璧勬簮 (姝ｅ父鎯呭喌涓嬩笉浼氬埌杈捐繖閲�)
    cleanup_vsim_resources();
    return 0;
}

