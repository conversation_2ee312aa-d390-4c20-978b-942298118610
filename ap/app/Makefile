#*******************************************************************************
# Default target
#*******************************************************************************
#zte_app := busybox nvserver adb

include config_app.mk

.PHONY: all $(zte_app) romfs

ALL: all romfs

all:
	touch .sgbuilt_flag
	for app in ${zte_app} ; do                                              \
		${MAKE} -j1 -C $$app all || exit $$?;                               \
	done;

romfs:
	mkdir -p $(ROOTFS_DIR)/bin
	if [ -f .sgbuilt_flag ]; then                                           \
		for app in ${zte_app} ; do                                          \
			${MAKE} -j1 -C $$app romfs || exit $$?;                         \
		done;                                                               \
	fi;

clean:
	for app in ${zte_app} ; do                                          \
		${MAKE} -j1 -C $$app clean;                                     \
	done;
	rm -f .sgbuilt_flag
