#*******************************************************************************
# include ZTE library makefile
#*******************************************************************************
include $(COMMON_MK)

##############USER COMIZE BEGIN################
#include ../net_team.mk

LIB_STATIC = libatutils.a
LIB_SHARED = libatutils.so

OBJS = $(patsubst %.c,%.o,$(wildcard *.c))

#CFLAGS += -I$(zte_app_path)/at_ctl/inc
#CFLAGS += -I$(zte_lib_path)/libatutils
CFLAGS += -I$(LINUX_DIR)/include
CFLAGS += -g -Werror=implicit-function-declaration


##############USER COMIZE END##################

CFLAGS += -I$(zte_app_path)/include
CFLAGS += -fPIC
LDFLAGS += -shared

all: $(LIB_STATIC) $(LIB_SHARED)

$(LIB_STATIC) : $(OBJS)
	$(AR) rcs $(LIB_STATIC) $(OBJS)

$(LIB_SHARED): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^

romfs:
	$(ROMFSINST) $(LIB_SHARED) /lib/$(LIB_SHARED)

clean:
	-$(RM) *.a *.o *.so $(LIB_SHARED) $(LIB_STATIC) $(OBJS)

