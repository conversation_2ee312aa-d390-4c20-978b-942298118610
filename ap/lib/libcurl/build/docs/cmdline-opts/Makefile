# Makefile.in generated by automake 1.16.5 from Makefile.am.
# docs/cmdline-opts/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.



#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# Copyright (C) 1998 - 2022, <PERSON>, <<EMAIL>>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################

#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# <AUTHOR> <EMAIL>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################
# Shared between Makefile.am and CMakeLists.txt
VPATH = ../../../curl-7.86.0/docs/cmdline-opts
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/curl
pkgincludedir = $(includedir)/curl
pkglibdir = $(libdir)/curl
pkglibexecdir = $(libexecdir)/curl
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-uclibc
host_triplet = arm-unknown-linux-gnu
subdir = docs/cmdline-opts
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/curl-amissl.m4 \
	$(top_srcdir)/m4/curl-bearssl.m4 \
	$(top_srcdir)/m4/curl-compilers.m4 \
	$(top_srcdir)/m4/curl-confopts.m4 \
	$(top_srcdir)/m4/curl-functions.m4 \
	$(top_srcdir)/m4/curl-gnutls.m4 \
	$(top_srcdir)/m4/curl-mbedtls.m4 $(top_srcdir)/m4/curl-nss.m4 \
	$(top_srcdir)/m4/curl-openssl.m4 \
	$(top_srcdir)/m4/curl-override.m4 \
	$(top_srcdir)/m4/curl-reentrant.m4 \
	$(top_srcdir)/m4/curl-rustls.m4 \
	$(top_srcdir)/m4/curl-schannel.m4 \
	$(top_srcdir)/m4/curl-sectransp.m4 \
	$(top_srcdir)/m4/curl-sysconfig.m4 \
	$(top_srcdir)/m4/curl-wolfssl.m4 $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/xc-am-iface.m4 \
	$(top_srcdir)/m4/xc-cc-check.m4 \
	$(top_srcdir)/m4/xc-lt-iface.m4 \
	$(top_srcdir)/m4/xc-translit.m4 \
	$(top_srcdir)/m4/xc-val-flgs.m4 \
	$(top_srcdir)/m4/zz40-xc-ovr.m4 \
	$(top_srcdir)/m4/zz50-xc-ovr.m4 \
	$(top_srcdir)/m4/zz60-xc-ovr.m4 $(top_srcdir)/acinclude.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/lib/curl_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
depcomp =
am__maybe_remake_depfiles =
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/Makefile.inc
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ar
AR_FLAGS = cr
AS = as
AUTOCONF = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' autoconf
AUTOHEADER = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' autoheader
AUTOMAKE = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' automake-1.16
AWK = gawk
BLANK_AT_MAKETIME = 
CC = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc -std=gnu11
CCDEPMODE = depmode=gcc3
CFLAGS = -g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -nostdinc -idirafter /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/include -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/uClibc/usr/include -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include -Werror-implicit-function-declaration -Wno-system-headers -pthread
CFLAG_CURL_SYMBOL_HIDING = -fvisibility=hidden
CONFIGURE_OPTIONS = " '--prefix=/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../lib/libcurl/install' '--target=arm-linux' '--host=arm-linux' '--enable-static' '--disable-shared' '--enable-threaded-resolver' '--without-libidn' '--without-ssl' '--without-librtmp' '--without-gnutls' '--without-nss' '--without-libssh2' '--without-zlib' '--without-winidn' '--disable-telnet' '--disable-tftp' '--disable-smtp' '--disable-imap' '--disable-pop3' '--disable-rtsp' '--disable-ldap' '--disable-ldaps' '--disable-ipv6' 'host_alias=arm-linux' 'target_alias=arm-linux' 'CC=/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc' 'CFLAGS=-g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -nostdinc  -idirafter /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/include -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/uClibc/usr/include -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include' 'LDFLAGS=-g -fno-common -fno-builtin -Wl,--gc-sections'"
CPP = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc -std=gnu11 -E
CPPFLAGS = 
CPPFLAG_CURL_STATICLIB = -DCURL_STATICLIB
CSCOPE = cscope
CTAGS = ctags
CURLVERSION = 7.86.0
CURL_CA_BUNDLE = 
CURL_CFLAG_EXTRAS = 
CURL_DISABLE_DICT = 
CURL_DISABLE_FILE = 
CURL_DISABLE_FTP = 
CURL_DISABLE_GOPHER = 
CURL_DISABLE_HTTP = 
CURL_DISABLE_IMAP = 1
CURL_DISABLE_LDAP = 1
CURL_DISABLE_LDAPS = 1
CURL_DISABLE_MQTT = 
CURL_DISABLE_POP3 = 1
CURL_DISABLE_PROXY = 
CURL_DISABLE_RTSP = 1
CURL_DISABLE_SMB = 
CURL_DISABLE_SMTP = 1
CURL_DISABLE_TELNET = 1
CURL_DISABLE_TFTP = 1
CURL_LT_SHLIB_VERSIONED_FLAVOUR = 
CURL_NETWORK_AND_TIME_LIBS = 
CURL_NETWORK_LIBS = 
CURL_PLIST_VERSION = 7.86.0
CURL_WITH_MULTI_SSL = 
CYGPATH_W = echo
DEFAULT_SSL_BACKEND = no
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
ENABLE_SHARED = no
ENABLE_STATIC = yes
ETAGS = etags
EXEEXT = 
FGREP = /usr/bin/grep -F
FILECMD = file
FISH_FUNCTIONS_DIR = ${prefix}/share/fish/vendor_completions.d
GCOV = 
GREP = /usr/bin/grep
HAVE_BROTLI = 
HAVE_GNUTLS_SRP = 
HAVE_LDAP_SSL = 
HAVE_LIBZ = 
HAVE_OPENSSL_SRP = 
HAVE_PROTO_BSDSOCKET_H = 
HAVE_ZSTD = 
IDN_ENABLED = 
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
IPV6_ENABLED = 
LCOV = 
LD = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ld
LDFLAGS = -g -fno-common -fno-builtin -Wl,--gc-sections
LIBCURL_LIBS = -pthread
LIBCURL_NO_SHARED =  -pthread
LIBOBJS = 
LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = #
MAKEINFO = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' makeinfo
MANIFEST_TOOL = :
MANOPT = -man
MKDIR_P = /usr/bin/mkdir -p
NM = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-linux-nm -B
NMEDIT = 
NROFF = /usr/bin/nroff
NSS_LIBS = 
OBJDUMP = arm-linux-objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = curl
PACKAGE_BUGREPORT = a suitable curl mailing list: https://curl.se/mail/
PACKAGE_NAME = curl
PACKAGE_STRING = curl -
PACKAGE_TARNAME = curl
PACKAGE_URL = 
PACKAGE_VERSION = -
PATH_SEPARATOR = :
PERL = /usr/bin/perl
PKGADD_NAME = curl - a client that groks URLs
PKGADD_PKG = HAXXcurl
PKGADD_VENDOR = curl.se
PKGCONFIG = no
RANDOM_FILE = 
RANLIB = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ranlib
RC = 
REQUIRE_LIB_DEPS = yes
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
SSL_BACKENDS = 
SSL_ENABLED = 
SSL_LIBS = 
STRIP = arm-linux-strip
SUPPORT_FEATURES = AsynchDNS Largefile UnixSockets alt-svc threadsafe
SUPPORT_PROTOCOLS = DICT FILE FTP GOPHER HTTP MQTT
USE_ARES = 
USE_BEARSSL = 
USE_GNUTLS = 
USE_HYPER = 
USE_LIBRTMP = 
USE_LIBSSH = 
USE_LIBSSH2 = 
USE_MBEDTLS = 
USE_MSH3 = 
USE_NGHTTP2 = 
USE_NGHTTP3 = 
USE_NGTCP2 = 
USE_NGTCP2_CRYPTO_GNUTLS = 
USE_NGTCP2_CRYPTO_OPENSSL = 
USE_NGTCP2_CRYPTO_WOLFSSL = 
USE_NSS = 
USE_OPENLDAP = 
USE_QUICHE = 
USE_RUSTLS = 
USE_SCHANNEL = 
USE_SECTRANSP = 
USE_UNIX_SOCKETS = 1
USE_WIN32_CRYPTO = 
USE_WIN32_LARGE_FILES = 
USE_WIN32_SMALL_FILES = 
USE_WINDOWS_SSPI = 
USE_WOLFSSH = 
USE_WOLFSSL = 
VERSION = -
VERSIONNUM = 075600
ZLIB_LIBS = 
ZSH_FUNCTIONS_DIR = ${prefix}/share/zsh/site-functions
abs_builddir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/docs/cmdline-opts
abs_srcdir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/../curl-7.86.0/docs/cmdline-opts
abs_top_builddir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build
abs_top_srcdir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/../curl-7.86.0
ac_ct_AR = 
ac_ct_CC = 
ac_ct_DUMPBIN = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-uclibc
build_alias = 
build_cpu = x86_64
build_os = linux-uclibc
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = arm-unknown-linux-gnu
host_alias = arm-linux
host_cpu = arm
host_os = linux-gnu
host_vendor = unknown
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
libext = a
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../lib/libcurl/install
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = ../../../curl-7.86.0/docs/cmdline-opts
sysconfdir = ${prefix}/etc
target_alias = arm-linux
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../../../curl-7.86.0
AUTOMAKE_OPTIONS = foreign no-dependencies
MANPAGE = $(top_builddir)/docs/curl.1
DPAGES = \
  abstract-unix-socket.d \
  alt-svc.d \
  anyauth.d \
  append.d \
  aws-sigv4.d \
  basic.d \
  cacert.d \
  capath.d \
  cert-status.d \
  cert-type.d \
  cert.d \
  ciphers.d \
  compressed-ssh.d \
  compressed.d \
  config.d \
  connect-timeout.d \
  connect-to.d \
  continue-at.d \
  cookie-jar.d \
  cookie.d \
  create-dirs.d \
  create-file-mode.d \
  crlf.d \
  crlfile.d \
  curves.d \
  data-ascii.d \
  data-binary.d \
  data-raw.d \
  data-urlencode.d \
  data.d \
  delegation.d \
  digest.d \
  disable-eprt.d \
  disable-epsv.d \
  disable.d \
  disallow-username-in-url.d \
  dns-interface.d \
  dns-ipv4-addr.d \
  dns-ipv6-addr.d \
  dns-servers.d \
  doh-cert-status.d \
  doh-insecure.d \
  doh-url.d \
  dump-header.d \
  egd-file.d \
  engine.d \
  etag-compare.d \
  etag-save.d \
  expect100-timeout.d \
  fail-early.d \
  fail-with-body.d \
  fail.d \
  false-start.d \
  form-escape.d \
  form-string.d \
  form.d \
  ftp-account.d \
  ftp-alternative-to-user.d \
  ftp-create-dirs.d \
  ftp-method.d \
  ftp-pasv.d \
  ftp-port.d \
  ftp-pret.d \
  ftp-skip-pasv-ip.d \
  ftp-ssl-ccc-mode.d \
  ftp-ssl-ccc.d \
  ftp-ssl-control.d \
  get.d \
  globoff.d \
  happy-eyeballs-timeout-ms.d \
  haproxy-protocol.d \
  head.d \
  header.d \
  help.d \
  hostpubmd5.d \
  hostpubsha256.d \
  hsts.d \
  http0.9.d \
  http1.0.d \
  http1.1.d \
  http2-prior-knowledge.d \
  http2.d \
  http3.d \
  ignore-content-length.d \
  include.d \
  insecure.d \
  interface.d \
  ipv4.d \
  ipv6.d \
  json.d \
  junk-session-cookies.d \
  keepalive-time.d \
  key-type.d \
  key.d \
  krb.d \
  libcurl.d \
  limit-rate.d \
  list-only.d \
  local-port.d \
  location-trusted.d \
  location.d \
  login-options.d \
  mail-auth.d \
  mail-from.d \
  mail-rcpt-allowfails.d \
  mail-rcpt.d \
  manual.d \
  max-filesize.d \
  max-redirs.d \
  max-time.d \
  metalink.d \
  negotiate.d \
  netrc-file.d \
  netrc-optional.d \
  netrc.d \
  next.d \
  no-alpn.d \
  no-buffer.d \
  no-clobber.d \
  no-keepalive.d \
  no-npn.d \
  no-progress-meter.d \
  no-sessionid.d \
  noproxy.d \
  ntlm-wb.d \
  ntlm.d \
  oauth2-bearer.d \
  output-dir.d \
  output.d \
  parallel-immediate.d \
  parallel-max.d \
  parallel.d \
  pass.d \
  path-as-is.d \
  pinnedpubkey.d \
  post301.d \
  post302.d \
  post303.d \
  preproxy.d \
  progress-bar.d \
  proto-default.d \
  proto-redir.d \
  proto.d \
  proxy-anyauth.d \
  proxy-basic.d \
  proxy-cacert.d \
  proxy-capath.d \
  proxy-cert-type.d \
  proxy-cert.d \
  proxy-ciphers.d \
  proxy-crlfile.d \
  proxy-digest.d \
  proxy-header.d \
  proxy-insecure.d \
  proxy-key-type.d \
  proxy-key.d \
  proxy-negotiate.d \
  proxy-ntlm.d \
  proxy-pass.d \
  proxy-pinnedpubkey.d \
  proxy-service-name.d \
  proxy-ssl-allow-beast.d \
  proxy-ssl-auto-client-cert.d \
  proxy-tls13-ciphers.d \
  proxy-tlsauthtype.d \
  proxy-tlspassword.d \
  proxy-tlsuser.d \
  proxy-tlsv1.d \
  proxy-user.d \
  proxy.d \
  proxy1.0.d \
  proxytunnel.d \
  pubkey.d \
  quote.d \
  random-file.d \
  range.d \
  rate.d \
  raw.d \
  referer.d \
  remote-header-name.d \
  remote-name-all.d \
  remote-name.d \
  remote-time.d \
  remove-on-error.d \
  request-target.d \
  request.d \
  resolve.d \
  retry-all-errors.d \
  retry-connrefused.d \
  retry-delay.d \
  retry-max-time.d \
  retry.d \
  sasl-authzid.d \
  sasl-ir.d \
  service-name.d \
  show-error.d \
  silent.d \
  socks4.d \
  socks4a.d \
  socks5-basic.d \
  socks5-gssapi-nec.d \
  socks5-gssapi-service.d \
  socks5-gssapi.d \
  socks5-hostname.d \
  socks5.d \
  speed-limit.d \
  speed-time.d \
  ssl-allow-beast.d \
  ssl-auto-client-cert.d \
  ssl-no-revoke.d \
  ssl-reqd.d \
  ssl-revoke-best-effort.d \
  ssl.d \
  sslv2.d \
  sslv3.d \
  stderr.d \
  styled-output.d \
  suppress-connect-headers.d \
  tcp-fastopen.d \
  tcp-nodelay.d \
  telnet-option.d \
  tftp-blksize.d \
  tftp-no-options.d \
  time-cond.d \
  tls-max.d \
  tls13-ciphers.d \
  tlsauthtype.d \
  tlspassword.d \
  tlsuser.d \
  tlsv1.0.d \
  tlsv1.1.d \
  tlsv1.2.d \
  tlsv1.3.d \
  tlsv1.d \
  tr-encoding.d \
  trace-ascii.d \
  trace-time.d \
  trace.d \
  unix-socket.d \
  upload-file.d \
  url.d \
  use-ascii.d \
  user-agent.d \
  user.d \
  verbose.d \
  version.d \
  write-out.d \
  xattr.d

OTHERPAGES = page-footer page-header
EXTRA_DIST = $(DPAGES) MANPAGE.md gen.pl $(OTHERPAGES) CMakeLists.txt
all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in: # $(srcdir)/Makefile.am $(srcdir)/Makefile.inc $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign docs/cmdline-opts/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign docs/cmdline-opts/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(srcdir)/Makefile.inc $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: # $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): # $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
tags TAGS:

ctags CTAGS:

cscope cscopelist:

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: all all-am check check-am clean clean-generic clean-libtool \
	cscopelist-am ctags-am distclean distclean-generic \
	distclean-libtool distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags-am uninstall uninstall-am

.PRECIOUS: Makefile


all: $(MANPAGE)

$(MANPAGE): $(DPAGES) $(OTHERPAGES) Makefile.inc
	@echo "generate $(MANPAGE)"
	@(cd $(srcdir) && /usr/bin/perl ./gen.pl mainpage $(DPAGES)) > $(MANPAGE)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
