# Makefile.in generated by automake 1.16.5 from Makefile.am.
# docs/libcurl/opts/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.



#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# Copyright (C) 1998 - 2022, <PERSON>, <<EMAIL>>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################

#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# <AUTHOR> <EMAIL>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################
# Shared between Makefile.am and CMakeLists.txt
VPATH = ../../../../curl-7.86.0/docs/libcurl/opts
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/curl
pkgincludedir = $(includedir)/curl
pkglibdir = $(libdir)/curl
pkglibexecdir = $(libexecdir)/curl
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-uclibc
host_triplet = arm-unknown-linux-gnu
subdir = docs/libcurl/opts
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/curl-amissl.m4 \
	$(top_srcdir)/m4/curl-bearssl.m4 \
	$(top_srcdir)/m4/curl-compilers.m4 \
	$(top_srcdir)/m4/curl-confopts.m4 \
	$(top_srcdir)/m4/curl-functions.m4 \
	$(top_srcdir)/m4/curl-gnutls.m4 \
	$(top_srcdir)/m4/curl-mbedtls.m4 $(top_srcdir)/m4/curl-nss.m4 \
	$(top_srcdir)/m4/curl-openssl.m4 \
	$(top_srcdir)/m4/curl-override.m4 \
	$(top_srcdir)/m4/curl-reentrant.m4 \
	$(top_srcdir)/m4/curl-rustls.m4 \
	$(top_srcdir)/m4/curl-schannel.m4 \
	$(top_srcdir)/m4/curl-sectransp.m4 \
	$(top_srcdir)/m4/curl-sysconfig.m4 \
	$(top_srcdir)/m4/curl-wolfssl.m4 $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/xc-am-iface.m4 \
	$(top_srcdir)/m4/xc-cc-check.m4 \
	$(top_srcdir)/m4/xc-lt-iface.m4 \
	$(top_srcdir)/m4/xc-translit.m4 \
	$(top_srcdir)/m4/xc-val-flgs.m4 \
	$(top_srcdir)/m4/zz40-xc-ovr.m4 \
	$(top_srcdir)/m4/zz50-xc-ovr.m4 \
	$(top_srcdir)/m4/zz60-xc-ovr.m4 $(top_srcdir)/acinclude.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/lib/curl_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
depcomp =
am__maybe_remake_depfiles =
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
man3dir = $(mandir)/man3
am__installdirs = "$(DESTDIR)$(man3dir)"
MANS = $(man_MANS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/Makefile.inc
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ar
AR_FLAGS = cr
AS = as
AUTOCONF = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' autoconf
AUTOHEADER = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' autoheader
AUTOMAKE = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' automake-1.16
AWK = gawk
BLANK_AT_MAKETIME = 
CC = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc -std=gnu11
CCDEPMODE = depmode=gcc3
CFLAGS = -g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -nostdinc -idirafter /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/include -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/uClibc/usr/include -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include -Werror-implicit-function-declaration -Wno-system-headers -pthread
CFLAG_CURL_SYMBOL_HIDING = -fvisibility=hidden
CONFIGURE_OPTIONS = " '--prefix=/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../lib/libcurl/install' '--target=arm-linux' '--host=arm-linux' '--enable-static' '--disable-shared' '--enable-threaded-resolver' '--without-libidn' '--without-ssl' '--without-librtmp' '--without-gnutls' '--without-nss' '--without-libssh2' '--without-zlib' '--without-winidn' '--disable-telnet' '--disable-tftp' '--disable-smtp' '--disable-imap' '--disable-pop3' '--disable-rtsp' '--disable-ldap' '--disable-ldaps' '--disable-ipv6' 'host_alias=arm-linux' 'target_alias=arm-linux' 'CC=/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc' 'CFLAGS=-g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -nostdinc  -idirafter /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/include -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/uClibc/usr/include -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include' 'LDFLAGS=-g -fno-common -fno-builtin -Wl,--gc-sections'"
CPP = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc -std=gnu11 -E
CPPFLAGS = 
CPPFLAG_CURL_STATICLIB = -DCURL_STATICLIB
CSCOPE = cscope
CTAGS = ctags
CURLVERSION = 7.86.0
CURL_CA_BUNDLE = 
CURL_CFLAG_EXTRAS = 
CURL_DISABLE_DICT = 
CURL_DISABLE_FILE = 
CURL_DISABLE_FTP = 
CURL_DISABLE_GOPHER = 
CURL_DISABLE_HTTP = 
CURL_DISABLE_IMAP = 1
CURL_DISABLE_LDAP = 1
CURL_DISABLE_LDAPS = 1
CURL_DISABLE_MQTT = 
CURL_DISABLE_POP3 = 1
CURL_DISABLE_PROXY = 
CURL_DISABLE_RTSP = 1
CURL_DISABLE_SMB = 
CURL_DISABLE_SMTP = 1
CURL_DISABLE_TELNET = 1
CURL_DISABLE_TFTP = 1
CURL_LT_SHLIB_VERSIONED_FLAVOUR = 
CURL_NETWORK_AND_TIME_LIBS = 
CURL_NETWORK_LIBS = 
CURL_PLIST_VERSION = 7.86.0
CURL_WITH_MULTI_SSL = 
CYGPATH_W = echo
DEFAULT_SSL_BACKEND = no
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
ENABLE_SHARED = no
ENABLE_STATIC = yes
ETAGS = etags
EXEEXT = 
FGREP = /usr/bin/grep -F
FILECMD = file
FISH_FUNCTIONS_DIR = ${prefix}/share/fish/vendor_completions.d
GCOV = 
GREP = /usr/bin/grep
HAVE_BROTLI = 
HAVE_GNUTLS_SRP = 
HAVE_LDAP_SSL = 
HAVE_LIBZ = 
HAVE_OPENSSL_SRP = 
HAVE_PROTO_BSDSOCKET_H = 
HAVE_ZSTD = 
IDN_ENABLED = 
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
IPV6_ENABLED = 
LCOV = 
LD = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ld
LDFLAGS = -g -fno-common -fno-builtin -Wl,--gc-sections
LIBCURL_LIBS = -pthread
LIBCURL_NO_SHARED =  -pthread
LIBOBJS = 
LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = #
MAKEINFO = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' makeinfo
MANIFEST_TOOL = :
MANOPT = -man
MKDIR_P = /usr/bin/mkdir -p
NM = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-linux-nm -B
NMEDIT = 
NROFF = /usr/bin/nroff
NSS_LIBS = 
OBJDUMP = arm-linux-objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = curl
PACKAGE_BUGREPORT = a suitable curl mailing list: https://curl.se/mail/
PACKAGE_NAME = curl
PACKAGE_STRING = curl -
PACKAGE_TARNAME = curl
PACKAGE_URL = 
PACKAGE_VERSION = -
PATH_SEPARATOR = :
PERL = /usr/bin/perl
PKGADD_NAME = curl - a client that groks URLs
PKGADD_PKG = HAXXcurl
PKGADD_VENDOR = curl.se
PKGCONFIG = no
RANDOM_FILE = 
RANLIB = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ranlib
RC = 
REQUIRE_LIB_DEPS = yes
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
SSL_BACKENDS = 
SSL_ENABLED = 
SSL_LIBS = 
STRIP = arm-linux-strip
SUPPORT_FEATURES = AsynchDNS Largefile UnixSockets alt-svc threadsafe
SUPPORT_PROTOCOLS = DICT FILE FTP GOPHER HTTP MQTT
USE_ARES = 
USE_BEARSSL = 
USE_GNUTLS = 
USE_HYPER = 
USE_LIBRTMP = 
USE_LIBSSH = 
USE_LIBSSH2 = 
USE_MBEDTLS = 
USE_MSH3 = 
USE_NGHTTP2 = 
USE_NGHTTP3 = 
USE_NGTCP2 = 
USE_NGTCP2_CRYPTO_GNUTLS = 
USE_NGTCP2_CRYPTO_OPENSSL = 
USE_NGTCP2_CRYPTO_WOLFSSL = 
USE_NSS = 
USE_OPENLDAP = 
USE_QUICHE = 
USE_RUSTLS = 
USE_SCHANNEL = 
USE_SECTRANSP = 
USE_UNIX_SOCKETS = 1
USE_WIN32_CRYPTO = 
USE_WIN32_LARGE_FILES = 
USE_WIN32_SMALL_FILES = 
USE_WINDOWS_SSPI = 
USE_WOLFSSH = 
USE_WOLFSSL = 
VERSION = -
VERSIONNUM = 075600
ZLIB_LIBS = 
ZSH_FUNCTIONS_DIR = ${prefix}/share/zsh/site-functions
abs_builddir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/docs/libcurl/opts
abs_srcdir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/../curl-7.86.0/docs/libcurl/opts
abs_top_builddir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build
abs_top_srcdir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/../curl-7.86.0
ac_ct_AR = 
ac_ct_CC = 
ac_ct_DUMPBIN = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-uclibc
build_alias = 
build_cpu = x86_64
build_os = linux-uclibc
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = arm-unknown-linux-gnu
host_alias = arm-linux
host_cpu = arm
host_os = linux-gnu
host_vendor = unknown
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
libext = a
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../lib/libcurl/install
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = ../../../../curl-7.86.0/docs/libcurl/opts
sysconfdir = ${prefix}/etc
target_alias = arm-linux
top_build_prefix = ../../../
top_builddir = ../../..
top_srcdir = ../../../../curl-7.86.0
AUTOMAKE_OPTIONS = foreign no-dependencies
man_MANS = \
  CURLINFO_ACTIVESOCKET.3                       \
  CURLINFO_APPCONNECT_TIME.3                    \
  CURLINFO_APPCONNECT_TIME_T.3                  \
  CURLINFO_CAINFO.3                             \
  CURLINFO_CAPATH.3                             \
  CURLINFO_CERTINFO.3                           \
  CURLINFO_CONDITION_UNMET.3                    \
  CURLINFO_CONNECT_TIME.3                       \
  CURLINFO_CONNECT_TIME_T.3                     \
  CURLINFO_CONTENT_LENGTH_DOWNLOAD.3            \
  CURLINFO_CONTENT_LENGTH_DOWNLOAD_T.3          \
  CURLINFO_CONTENT_LENGTH_UPLOAD.3              \
  CURLINFO_CONTENT_LENGTH_UPLOAD_T.3            \
  CURLINFO_CONTENT_TYPE.3                       \
  CURLINFO_COOKIELIST.3                         \
  CURLINFO_EFFECTIVE_METHOD.3                   \
  CURLINFO_EFFECTIVE_URL.3                      \
  CURLINFO_FILETIME.3                           \
  CURLINFO_FILETIME_T.3                         \
  CURLINFO_FTP_ENTRY_PATH.3                     \
  CURLINFO_HEADER_SIZE.3                        \
  CURLINFO_HTTP_CONNECTCODE.3                   \
  CURLINFO_HTTP_VERSION.3                       \
  CURLINFO_HTTPAUTH_AVAIL.3                     \
  CURLINFO_LASTSOCKET.3                         \
  CURLINFO_LOCAL_IP.3                           \
  CURLINFO_LOCAL_PORT.3                         \
  CURLINFO_NAMELOOKUP_TIME.3                    \
  CURLINFO_NAMELOOKUP_TIME_T.3                  \
  CURLINFO_NUM_CONNECTS.3                       \
  CURLINFO_OS_ERRNO.3                           \
  CURLINFO_PRETRANSFER_TIME.3                   \
  CURLINFO_PRETRANSFER_TIME_T.3                 \
  CURLINFO_PRIMARY_IP.3                         \
  CURLINFO_PRIMARY_PORT.3                       \
  CURLINFO_PRIVATE.3                            \
  CURLINFO_PROTOCOL.3                           \
  CURLINFO_PROXY_ERROR.3                        \
  CURLINFO_PROXY_SSL_VERIFYRESULT.3             \
  CURLINFO_PROXYAUTH_AVAIL.3                    \
  CURLINFO_REDIRECT_COUNT.3                     \
  CURLINFO_REDIRECT_TIME.3                      \
  CURLINFO_REDIRECT_TIME_T.3                    \
  CURLINFO_REDIRECT_URL.3                       \
  CURLINFO_REFERER.3                            \
  CURLINFO_REQUEST_SIZE.3                       \
  CURLINFO_RESPONSE_CODE.3                      \
  CURLINFO_RETRY_AFTER.3                        \
  CURLINFO_RTSP_CLIENT_CSEQ.3                   \
  CURLINFO_RTSP_CSEQ_RECV.3                     \
  CURLINFO_RTSP_SERVER_CSEQ.3                   \
  CURLINFO_RTSP_SESSION_ID.3                    \
  CURLINFO_SCHEME.3                             \
  CURLINFO_SIZE_DOWNLOAD.3                      \
  CURLINFO_SIZE_DOWNLOAD_T.3                    \
  CURLINFO_SIZE_UPLOAD.3                        \
  CURLINFO_SIZE_UPLOAD_T.3                      \
  CURLINFO_SPEED_DOWNLOAD.3                     \
  CURLINFO_SPEED_DOWNLOAD_T.3                   \
  CURLINFO_SPEED_UPLOAD.3                       \
  CURLINFO_SPEED_UPLOAD_T.3                     \
  CURLINFO_SSL_ENGINES.3                        \
  CURLINFO_SSL_VERIFYRESULT.3                   \
  CURLINFO_STARTTRANSFER_TIME.3                 \
  CURLINFO_STARTTRANSFER_TIME_T.3               \
  CURLINFO_TLS_SESSION.3                        \
  CURLINFO_TLS_SSL_PTR.3                        \
  CURLINFO_TOTAL_TIME.3                         \
  CURLINFO_TOTAL_TIME_T.3                       \
  CURLMOPT_CHUNK_LENGTH_PENALTY_SIZE.3          \
  CURLMOPT_CONTENT_LENGTH_PENALTY_SIZE.3        \
  CURLMOPT_MAX_CONCURRENT_STREAMS.3             \
  CURLMOPT_MAX_HOST_CONNECTIONS.3               \
  CURLMOPT_MAX_PIPELINE_LENGTH.3                \
  CURLMOPT_MAX_TOTAL_CONNECTIONS.3              \
  CURLMOPT_MAXCONNECTS.3                        \
  CURLMOPT_PIPELINING.3                         \
  CURLMOPT_PIPELINING_SERVER_BL.3               \
  CURLMOPT_PIPELINING_SITE_BL.3                 \
  CURLMOPT_PUSHDATA.3                           \
  CURLMOPT_PUSHFUNCTION.3                       \
  CURLMOPT_SOCKETDATA.3                         \
  CURLMOPT_SOCKETFUNCTION.3                     \
  CURLMOPT_TIMERDATA.3                          \
  CURLMOPT_TIMERFUNCTION.3                      \
  CURLOPT_ABSTRACT_UNIX_SOCKET.3                \
  CURLOPT_ACCEPT_ENCODING.3                     \
  CURLOPT_ACCEPTTIMEOUT_MS.3                    \
  CURLOPT_ADDRESS_SCOPE.3                       \
  CURLOPT_ALTSVC.3                              \
  CURLOPT_ALTSVC_CTRL.3                         \
  CURLOPT_APPEND.3                              \
  CURLOPT_AUTOREFERER.3                         \
  CURLOPT_AWS_SIGV4.3                           \
  CURLOPT_BUFFERSIZE.3                          \
  CURLOPT_CAINFO.3                              \
  CURLOPT_CAINFO_BLOB.3                         \
  CURLOPT_CAPATH.3                              \
  CURLOPT_CERTINFO.3                            \
  CURLOPT_CHUNK_BGN_FUNCTION.3                  \
  CURLOPT_CHUNK_DATA.3                          \
  CURLOPT_CHUNK_END_FUNCTION.3                  \
  CURLOPT_CLOSESOCKETDATA.3                     \
  CURLOPT_CLOSESOCKETFUNCTION.3                 \
  CURLOPT_CONNECT_ONLY.3                        \
  CURLOPT_CONNECT_TO.3                          \
  CURLOPT_CONNECTTIMEOUT.3                      \
  CURLOPT_CONNECTTIMEOUT_MS.3                   \
  CURLOPT_CONV_FROM_NETWORK_FUNCTION.3          \
  CURLOPT_CONV_FROM_UTF8_FUNCTION.3             \
  CURLOPT_CONV_TO_NETWORK_FUNCTION.3            \
  CURLOPT_COOKIE.3                              \
  CURLOPT_COOKIEFILE.3                          \
  CURLOPT_COOKIEJAR.3                           \
  CURLOPT_COOKIELIST.3                          \
  CURLOPT_COOKIESESSION.3                       \
  CURLOPT_COPYPOSTFIELDS.3                      \
  CURLOPT_CRLF.3                                \
  CURLOPT_CRLFILE.3                             \
  CURLOPT_CURLU.3                               \
  CURLOPT_CUSTOMREQUEST.3                       \
  CURLOPT_DEBUGDATA.3                           \
  CURLOPT_DEBUGFUNCTION.3                       \
  CURLOPT_DEFAULT_PROTOCOL.3                    \
  CURLOPT_DIRLISTONLY.3                         \
  CURLOPT_DISALLOW_USERNAME_IN_URL.3            \
  CURLOPT_DNS_CACHE_TIMEOUT.3                   \
  CURLOPT_DNS_INTERFACE.3                       \
  CURLOPT_DNS_LOCAL_IP4.3                       \
  CURLOPT_DNS_LOCAL_IP6.3                       \
  CURLOPT_DNS_SERVERS.3                         \
  CURLOPT_DNS_SHUFFLE_ADDRESSES.3               \
  CURLOPT_DNS_USE_GLOBAL_CACHE.3                \
  CURLOPT_DOH_SSL_VERIFYHOST.3                  \
  CURLOPT_DOH_SSL_VERIFYPEER.3                  \
  CURLOPT_DOH_SSL_VERIFYSTATUS.3                \
  CURLOPT_DOH_URL.3                             \
  CURLOPT_EGDSOCKET.3                           \
  CURLOPT_ERRORBUFFER.3                         \
  CURLOPT_EXPECT_100_TIMEOUT_MS.3               \
  CURLOPT_FAILONERROR.3                         \
  CURLOPT_FILETIME.3                            \
  CURLOPT_FNMATCH_DATA.3                        \
  CURLOPT_FNMATCH_FUNCTION.3                    \
  CURLOPT_FOLLOWLOCATION.3                      \
  CURLOPT_FORBID_REUSE.3                        \
  CURLOPT_FRESH_CONNECT.3                       \
  CURLOPT_FTP_ACCOUNT.3                         \
  CURLOPT_FTP_ALTERNATIVE_TO_USER.3             \
  CURLOPT_FTP_CREATE_MISSING_DIRS.3             \
  CURLOPT_FTP_FILEMETHOD.3                      \
  CURLOPT_FTP_SKIP_PASV_IP.3                    \
  CURLOPT_FTP_SSL_CCC.3                         \
  CURLOPT_FTP_USE_EPRT.3                        \
  CURLOPT_FTP_USE_EPSV.3                        \
  CURLOPT_FTP_USE_PRET.3                        \
  CURLOPT_FTPPORT.3                             \
  CURLOPT_FTPSSLAUTH.3                          \
  CURLOPT_GSSAPI_DELEGATION.3                   \
  CURLOPT_HAPPY_EYEBALLS_TIMEOUT_MS.3           \
  CURLOPT_HAPROXYPROTOCOL.3                     \
  CURLOPT_HEADER.3                              \
  CURLOPT_HEADERDATA.3                          \
  CURLOPT_HEADERFUNCTION.3                      \
  CURLOPT_HEADEROPT.3                           \
  CURLOPT_HSTS.3                                \
  CURLOPT_HSTS_CTRL.3                           \
  CURLOPT_HSTSREADDATA.3                        \
  CURLOPT_HSTSREADFUNCTION.3                    \
  CURLOPT_HSTSWRITEDATA.3                       \
  CURLOPT_HSTSWRITEFUNCTION.3                   \
  CURLOPT_HTTP09_ALLOWED.3                      \
  CURLOPT_HTTP200ALIASES.3                      \
  CURLOPT_HTTP_CONTENT_DECODING.3               \
  CURLOPT_HTTP_TRANSFER_DECODING.3              \
  CURLOPT_HTTP_VERSION.3                        \
  CURLOPT_HTTPAUTH.3                            \
  CURLOPT_HTTPGET.3                             \
  CURLOPT_HTTPHEADER.3                          \
  CURLOPT_HTTPPOST.3                            \
  CURLOPT_HTTPPROXYTUNNEL.3                     \
  CURLOPT_IGNORE_CONTENT_LENGTH.3               \
  CURLOPT_INFILESIZE.3                          \
  CURLOPT_INFILESIZE_LARGE.3                    \
  CURLOPT_INTERFACE.3                           \
  CURLOPT_INTERLEAVEDATA.3                      \
  CURLOPT_INTERLEAVEFUNCTION.3                  \
  CURLOPT_IOCTLDATA.3                           \
  CURLOPT_IOCTLFUNCTION.3                       \
  CURLOPT_IPRESOLVE.3                           \
  CURLOPT_ISSUERCERT.3                          \
  CURLOPT_ISSUERCERT_BLOB.3                     \
  CURLOPT_KEEP_SENDING_ON_ERROR.3               \
  CURLOPT_KEYPASSWD.3                           \
  CURLOPT_KRBLEVEL.3                            \
  CURLOPT_LOCALPORT.3                           \
  CURLOPT_LOCALPORTRANGE.3                      \
  CURLOPT_LOGIN_OPTIONS.3                       \
  CURLOPT_LOW_SPEED_LIMIT.3                     \
  CURLOPT_LOW_SPEED_TIME.3                      \
  CURLOPT_MAIL_AUTH.3                           \
  CURLOPT_MAIL_FROM.3                           \
  CURLOPT_MAIL_RCPT.3                           \
  CURLOPT_MAIL_RCPT_ALLLOWFAILS.3               \
  CURLOPT_MAX_RECV_SPEED_LARGE.3                \
  CURLOPT_MAX_SEND_SPEED_LARGE.3                \
  CURLOPT_MAXAGE_CONN.3                         \
  CURLOPT_MAXCONNECTS.3                         \
  CURLOPT_MAXFILESIZE.3                         \
  CURLOPT_MAXFILESIZE_LARGE.3                   \
  CURLOPT_MAXLIFETIME_CONN.3                    \
  CURLOPT_MAXREDIRS.3                           \
  CURLOPT_MIME_OPTIONS.3                        \
  CURLOPT_MIMEPOST.3                            \
  CURLOPT_NETRC.3                               \
  CURLOPT_NETRC_FILE.3                          \
  CURLOPT_NEW_DIRECTORY_PERMS.3                 \
  CURLOPT_NEW_FILE_PERMS.3                      \
  CURLOPT_NOBODY.3                              \
  CURLOPT_NOPROGRESS.3                          \
  CURLOPT_NOPROXY.3                             \
  CURLOPT_NOSIGNAL.3                            \
  CURLOPT_OPENSOCKETDATA.3                      \
  CURLOPT_OPENSOCKETFUNCTION.3                  \
  CURLOPT_PASSWORD.3                            \
  CURLOPT_PATH_AS_IS.3                          \
  CURLOPT_PINNEDPUBLICKEY.3                     \
  CURLOPT_PIPEWAIT.3                            \
  CURLOPT_PORT.3                                \
  CURLOPT_POST.3                                \
  CURLOPT_POSTFIELDS.3                          \
  CURLOPT_POSTFIELDSIZE.3                       \
  CURLOPT_POSTFIELDSIZE_LARGE.3                 \
  CURLOPT_POSTQUOTE.3                           \
  CURLOPT_POSTREDIR.3                           \
  CURLOPT_PRE_PROXY.3                           \
  CURLOPT_PREQUOTE.3                            \
  CURLOPT_PREREQDATA.3                          \
  CURLOPT_PREREQFUNCTION.3                      \
  CURLOPT_PRIVATE.3                             \
  CURLOPT_PROGRESSDATA.3                        \
  CURLOPT_PROGRESSFUNCTION.3                    \
  CURLOPT_PROTOCOLS.3                           \
  CURLOPT_PROTOCOLS_STR.3                       \
  CURLOPT_PROXY.3                               \
  CURLOPT_PROXY_CAINFO.3                        \
  CURLOPT_PROXY_CAINFO_BLOB.3                   \
  CURLOPT_PROXY_CAPATH.3                        \
  CURLOPT_PROXY_CRLFILE.3                       \
  CURLOPT_PROXY_ISSUERCERT.3                    \
  CURLOPT_PROXY_ISSUERCERT_BLOB.3               \
  CURLOPT_PROXY_KEYPASSWD.3                     \
  CURLOPT_PROXY_PINNEDPUBLICKEY.3               \
  CURLOPT_PROXY_SERVICE_NAME.3                  \
  CURLOPT_PROXY_SSL_CIPHER_LIST.3               \
  CURLOPT_PROXY_SSL_OPTIONS.3                   \
  CURLOPT_PROXY_SSL_VERIFYHOST.3                \
  CURLOPT_PROXY_SSL_VERIFYPEER.3                \
  CURLOPT_PROXY_SSLCERT.3                       \
  CURLOPT_PROXY_SSLCERT_BLOB.3                  \
  CURLOPT_PROXY_SSLCERTTYPE.3                   \
  CURLOPT_PROXY_SSLKEY.3                        \
  CURLOPT_PROXY_SSLKEY_BLOB.3                   \
  CURLOPT_PROXY_SSLKEYTYPE.3                    \
  CURLOPT_PROXY_SSLVERSION.3                    \
  CURLOPT_PROXY_TLS13_CIPHERS.3                 \
  CURLOPT_PROXY_TLSAUTH_PASSWORD.3              \
  CURLOPT_PROXY_TLSAUTH_TYPE.3                  \
  CURLOPT_PROXY_TLSAUTH_USERNAME.3              \
  CURLOPT_PROXY_TRANSFER_MODE.3                 \
  CURLOPT_PROXYAUTH.3                           \
  CURLOPT_PROXYHEADER.3                         \
  CURLOPT_PROXYPASSWORD.3                       \
  CURLOPT_PROXYPORT.3                           \
  CURLOPT_PROXYTYPE.3                           \
  CURLOPT_PROXYUSERNAME.3                       \
  CURLOPT_PROXYUSERPWD.3                        \
  CURLOPT_PUT.3                                 \
  CURLOPT_QUOTE.3                               \
  CURLOPT_RANDOM_FILE.3                         \
  CURLOPT_RANGE.3                               \
  CURLOPT_READDATA.3                            \
  CURLOPT_READFUNCTION.3                        \
  CURLOPT_REDIR_PROTOCOLS.3                     \
  CURLOPT_REDIR_PROTOCOLS_STR.3                 \
  CURLOPT_REFERER.3                             \
  CURLOPT_REQUEST_TARGET.3                      \
  CURLOPT_RESOLVE.3                             \
  CURLOPT_RESOLVER_START_DATA.3                 \
  CURLOPT_RESOLVER_START_FUNCTION.3             \
  CURLOPT_RESUME_FROM.3                         \
  CURLOPT_RESUME_FROM_LARGE.3                   \
  CURLOPT_RTSP_CLIENT_CSEQ.3                    \
  CURLOPT_RTSP_REQUEST.3                        \
  CURLOPT_RTSP_SERVER_CSEQ.3                    \
  CURLOPT_RTSP_SESSION_ID.3                     \
  CURLOPT_RTSP_STREAM_URI.3                     \
  CURLOPT_RTSP_TRANSPORT.3                      \
  CURLOPT_SASL_AUTHZID.3                        \
  CURLOPT_SASL_IR.3                             \
  CURLOPT_SEEKDATA.3                            \
  CURLOPT_SEEKFUNCTION.3                        \
  CURLOPT_SERVER_RESPONSE_TIMEOUT.3             \
  CURLOPT_SERVICE_NAME.3                        \
  CURLOPT_SHARE.3                               \
  CURLOPT_SOCKOPTDATA.3                         \
  CURLOPT_SOCKOPTFUNCTION.3                     \
  CURLOPT_SOCKS5_AUTH.3                         \
  CURLOPT_SOCKS5_GSSAPI_NEC.3                   \
  CURLOPT_SOCKS5_GSSAPI_SERVICE.3               \
  CURLOPT_SSH_AUTH_TYPES.3                      \
  CURLOPT_SSH_COMPRESSION.3                     \
  CURLOPT_SSH_HOST_PUBLIC_KEY_MD5.3             \
  CURLOPT_SSH_HOST_PUBLIC_KEY_SHA256.3          \
  CURLOPT_SSH_KEYDATA.3                         \
  CURLOPT_SSH_KEYFUNCTION.3                     \
  CURLOPT_SSH_KNOWNHOSTS.3                      \
  CURLOPT_SSH_PRIVATE_KEYFILE.3                 \
  CURLOPT_SSH_PUBLIC_KEYFILE.3                  \
  CURLOPT_SSL_CIPHER_LIST.3                     \
  CURLOPT_SSL_CTX_DATA.3                        \
  CURLOPT_SSL_CTX_FUNCTION.3                    \
  CURLOPT_SSL_EC_CURVES.3			\
  CURLOPT_SSL_ENABLE_ALPN.3                     \
  CURLOPT_SSL_ENABLE_NPN.3                      \
  CURLOPT_SSL_FALSESTART.3                      \
  CURLOPT_SSL_OPTIONS.3                         \
  CURLOPT_SSL_SESSIONID_CACHE.3                 \
  CURLOPT_SSL_VERIFYHOST.3                      \
  CURLOPT_SSL_VERIFYPEER.3                      \
  CURLOPT_SSL_VERIFYSTATUS.3                    \
  CURLOPT_SSLCERT.3                             \
  CURLOPT_SSLCERT_BLOB.3                        \
  CURLOPT_SSLCERTTYPE.3                         \
  CURLOPT_SSLENGINE.3                           \
  CURLOPT_SSLENGINE_DEFAULT.3                   \
  CURLOPT_SSLKEY.3                              \
  CURLOPT_SSLKEY_BLOB.3                         \
  CURLOPT_SSLKEYTYPE.3                          \
  CURLOPT_SSLVERSION.3                          \
  CURLOPT_STDERR.3                              \
  CURLOPT_STREAM_DEPENDS.3                      \
  CURLOPT_STREAM_DEPENDS_E.3                    \
  CURLOPT_STREAM_WEIGHT.3                       \
  CURLOPT_SUPPRESS_CONNECT_HEADERS.3            \
  CURLOPT_TCP_FASTOPEN.3                        \
  CURLOPT_TCP_KEEPALIVE.3                       \
  CURLOPT_TCP_KEEPIDLE.3                        \
  CURLOPT_TCP_KEEPINTVL.3                       \
  CURLOPT_TCP_NODELAY.3                         \
  CURLOPT_TELNETOPTIONS.3                       \
  CURLOPT_TFTP_BLKSIZE.3                        \
  CURLOPT_TFTP_NO_OPTIONS.3                     \
  CURLOPT_TIMECONDITION.3                       \
  CURLOPT_TIMEOUT.3                             \
  CURLOPT_TIMEOUT_MS.3                          \
  CURLOPT_TIMEVALUE.3                           \
  CURLOPT_TIMEVALUE_LARGE.3                     \
  CURLOPT_TLS13_CIPHERS.3                       \
  CURLOPT_TLSAUTH_PASSWORD.3                    \
  CURLOPT_TLSAUTH_TYPE.3                        \
  CURLOPT_TLSAUTH_USERNAME.3                    \
  CURLOPT_TRAILERDATA.3                         \
  CURLOPT_TRAILERFUNCTION.3                     \
  CURLOPT_TRANSFER_ENCODING.3                   \
  CURLOPT_TRANSFERTEXT.3                        \
  CURLOPT_UNIX_SOCKET_PATH.3                    \
  CURLOPT_UNRESTRICTED_AUTH.3                   \
  CURLOPT_UPKEEP_INTERVAL_MS.3                  \
  CURLOPT_UPLOAD.3                              \
  CURLOPT_UPLOAD_BUFFERSIZE.3                   \
  CURLOPT_URL.3                                 \
  CURLOPT_USE_SSL.3                             \
  CURLOPT_USERAGENT.3                           \
  CURLOPT_USERNAME.3                            \
  CURLOPT_USERPWD.3                             \
  CURLOPT_VERBOSE.3                             \
  CURLOPT_WILDCARDMATCH.3                       \
  CURLOPT_WRITEDATA.3                           \
  CURLOPT_WRITEFUNCTION.3                       \
  CURLOPT_WS_OPTIONS.3                          \
  CURLOPT_XFERINFODATA.3                        \
  CURLOPT_XFERINFOFUNCTION.3                    \
  CURLOPT_XOAUTH2_BEARER.3                      \
  CURLSHOPT_LOCKFUNC.3                          \
  CURLSHOPT_SHARE.3                             \
  CURLSHOPT_UNLOCKFUNC.3                        \
  CURLSHOPT_UNSHARE.3                           \
  CURLSHOPT_USERDATA.3

man_DISTMANS = $(man_MANS:.3=.3.dist)
HTMLPAGES = $(man_MANS:.3=.html)
PDFPAGES = $(man_MANS:.3=.pdf)
CLEANFILES = $(HTMLPAGES) $(PDFPAGES) $(man_DISTMANS)
EXTRA_DIST = $(man_MANS) CMakeLists.txt
MAN2HTML = roffit --mandir=. $< >$@
SUFFIXES = .3 .html
all: all-am

.SUFFIXES:
.SUFFIXES: .3 .html .pdf
$(srcdir)/Makefile.in: # $(srcdir)/Makefile.am $(srcdir)/Makefile.inc $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign docs/libcurl/opts/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign docs/libcurl/opts/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(srcdir)/Makefile.inc $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: # $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): # $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-man3: $(man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(man_MANS)'; \
	test -n "$(man3dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man3dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man3dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.3[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^3][0-9a-z]*$$,3,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man3dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man3dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man3dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man3dir)" || exit $$?; }; \
	done; }

uninstall-man3:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man3dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.3[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^3][0-9a-z]*$$,3,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man3dir)'; $(am__uninstall_files_from_dir)
tags TAGS:

ctags CTAGS:

cscope cscopelist:

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(MANS)
installdirs:
	for dir in "$(DESTDIR)$(man3dir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html-am:

info: info-am

info-am:

install-data-am: install-man

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man: install-man3

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-man

uninstall-man: uninstall-man3

.MAKE: install-am install-strip

.PHONY: all all-am check check-am clean clean-generic clean-libtool \
	cscopelist-am ctags-am distclean distclean-generic \
	distclean-libtool distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-man3 install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-generic mostlyclean-libtool pdf pdf-am \
	ps ps-am tags-am uninstall uninstall-am uninstall-man \
	uninstall-man3

.PRECIOUS: Makefile


html: $(HTMLPAGES)

.3.html:
	$(MAN2HTML)

pdf: $(PDFPAGES)

.3.pdf:
	@(foo=`echo $@ | sed -e 's/\.[0-9]$$//g'`; \
	groff -Tps -man $< >$$foo.ps; \
	ps2pdf $$foo.ps $@; \
	rm $$foo.ps; \
	echo "converted $< to $@")

mancheck:
	@cd $(top_srcdir)/docs/libcurl/opts && ls `awk -F, '!/OBSOLETE/ && /^  CINIT/ { a=substr($$1, 9); print "CURLOPT_" a ".3"}' $(top_srcdir)/include/curl/curl.h`
	rm -f in_temp
	@(for a in $(man_MANS); do echo $$a >>in_temp; done)
	sort in_temp > in_makefile
	ls CURL*.3 > in_directory
	-diff -u in_makefile in_directory
	rm in_temp in_directory in_makefile

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
