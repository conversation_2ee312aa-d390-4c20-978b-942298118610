# Makefile.in generated by automake 1.16.5 from Makefile.am.
# docs/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.



#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# Copyright (C) 1998 - 2022, <PERSON>, <<EMAIL>>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################
VPATH = ../../curl-7.86.0/docs
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/curl
pkgincludedir = $(includedir)/curl
pkglibdir = $(libdir)/curl
pkglibexecdir = $(libexecdir)/curl
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-uclibc
host_triplet = arm-unknown-linux-gnu
subdir = docs
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/curl-amissl.m4 \
	$(top_srcdir)/m4/curl-bearssl.m4 \
	$(top_srcdir)/m4/curl-compilers.m4 \
	$(top_srcdir)/m4/curl-confopts.m4 \
	$(top_srcdir)/m4/curl-functions.m4 \
	$(top_srcdir)/m4/curl-gnutls.m4 \
	$(top_srcdir)/m4/curl-mbedtls.m4 $(top_srcdir)/m4/curl-nss.m4 \
	$(top_srcdir)/m4/curl-openssl.m4 \
	$(top_srcdir)/m4/curl-override.m4 \
	$(top_srcdir)/m4/curl-reentrant.m4 \
	$(top_srcdir)/m4/curl-rustls.m4 \
	$(top_srcdir)/m4/curl-schannel.m4 \
	$(top_srcdir)/m4/curl-sectransp.m4 \
	$(top_srcdir)/m4/curl-sysconfig.m4 \
	$(top_srcdir)/m4/curl-wolfssl.m4 $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/xc-am-iface.m4 \
	$(top_srcdir)/m4/xc-cc-check.m4 \
	$(top_srcdir)/m4/xc-lt-iface.m4 \
	$(top_srcdir)/m4/xc-translit.m4 \
	$(top_srcdir)/m4/xc-val-flgs.m4 \
	$(top_srcdir)/m4/zz40-xc-ovr.m4 \
	$(top_srcdir)/m4/zz50-xc-ovr.m4 \
	$(top_srcdir)/m4/zz60-xc-ovr.m4 $(top_srcdir)/acinclude.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/lib/curl_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
depcomp =
am__maybe_remake_depfiles =
SOURCES =
DIST_SOURCES =
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
man1dir = $(mandir)/man1
am__installdirs = "$(DESTDIR)$(man1dir)"
MANS = $(dist_man_MANS) $(man_MANS)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(dist_man_MANS) $(srcdir)/Makefile.in INSTALL \
	README.md THANKS TODO
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ar
AR_FLAGS = cr
AS = as
AUTOCONF = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' autoconf
AUTOHEADER = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' autoheader
AUTOMAKE = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' automake-1.16
AWK = gawk
BLANK_AT_MAKETIME = 
CC = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc -std=gnu11
CCDEPMODE = depmode=gcc3
CFLAGS = -g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -nostdinc -idirafter /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/include -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/uClibc/usr/include -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed -isystem /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include -Werror-implicit-function-declaration -Wno-system-headers -pthread
CFLAG_CURL_SYMBOL_HIDING = -fvisibility=hidden
CONFIGURE_OPTIONS = " '--prefix=/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../lib/libcurl/install' '--target=arm-linux' '--host=arm-linux' '--enable-static' '--disable-shared' '--enable-threaded-resolver' '--without-libidn' '--without-ssl' '--without-librtmp' '--without-gnutls' '--without-nss' '--without-libssh2' '--without-zlib' '--without-winidn' '--disable-telnet' '--disable-tftp' '--disable-smtp' '--disable-imap' '--disable-pop3' '--disable-rtsp' '--disable-ldap' '--disable-ldaps' '--disable-ipv6' 'host_alias=arm-linux' 'target_alias=arm-linux' 'CC=/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc' 'CFLAGS=-g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -nostdinc  -idirafter /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/include -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../staging/uClibc/usr/include -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed -isystem   /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin/../lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include' 'LDFLAGS=-g -fno-common -fno-builtin -Wl,--gc-sections'"
CPP = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-gcc -std=gnu11 -E
CPPFLAGS = 
CPPFLAG_CURL_STATICLIB = -DCURL_STATICLIB
CSCOPE = cscope
CTAGS = ctags
CURLVERSION = 7.86.0
CURL_CA_BUNDLE = 
CURL_CFLAG_EXTRAS = 
CURL_DISABLE_DICT = 
CURL_DISABLE_FILE = 
CURL_DISABLE_FTP = 
CURL_DISABLE_GOPHER = 
CURL_DISABLE_HTTP = 
CURL_DISABLE_IMAP = 1
CURL_DISABLE_LDAP = 1
CURL_DISABLE_LDAPS = 1
CURL_DISABLE_MQTT = 
CURL_DISABLE_POP3 = 1
CURL_DISABLE_PROXY = 
CURL_DISABLE_RTSP = 1
CURL_DISABLE_SMB = 
CURL_DISABLE_SMTP = 1
CURL_DISABLE_TELNET = 1
CURL_DISABLE_TFTP = 1
CURL_LT_SHLIB_VERSIONED_FLAVOUR = 
CURL_NETWORK_AND_TIME_LIBS = 
CURL_NETWORK_LIBS = 
CURL_PLIST_VERSION = 7.86.0
CURL_WITH_MULTI_SSL = 
CYGPATH_W = echo
DEFAULT_SSL_BACKEND = no
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
ENABLE_SHARED = no
ENABLE_STATIC = yes
ETAGS = etags
EXEEXT = 
FGREP = /usr/bin/grep -F
FILECMD = file
FISH_FUNCTIONS_DIR = ${prefix}/share/fish/vendor_completions.d
GCOV = 
GREP = /usr/bin/grep
HAVE_BROTLI = 
HAVE_GNUTLS_SRP = 
HAVE_LDAP_SSL = 
HAVE_LIBZ = 
HAVE_OPENSSL_SRP = 
HAVE_PROTO_BSDSOCKET_H = 
HAVE_ZSTD = 
IDN_ENABLED = 
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
IPV6_ENABLED = 
LCOV = 
LD = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ld
LDFLAGS = -g -fno-common -fno-builtin -Wl,--gc-sections
LIBCURL_LIBS = -pthread
LIBCURL_NO_SHARED =  -pthread
LIBOBJS = 
LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = #
MAKEINFO = ${SHELL} '/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/missing' makeinfo
MANIFEST_TOOL = :
MANOPT = -man
MKDIR_P = /usr/bin/mkdir -p
NM = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-linux-nm -B
NMEDIT = 
NROFF = /usr/bin/nroff
NSS_LIBS = 
OBJDUMP = arm-linux-objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = curl
PACKAGE_BUGREPORT = a suitable curl mailing list: https://curl.se/mail/
PACKAGE_NAME = curl
PACKAGE_STRING = curl -
PACKAGE_TARNAME = curl
PACKAGE_URL = 
PACKAGE_VERSION = -
PATH_SEPARATOR = :
PERL = /usr/bin/perl
PKGADD_NAME = curl - a client that groks URLs
PKGADD_PKG = HAXXcurl
PKGADD_VENDOR = curl.se
PKGCONFIG = no
RANDOM_FILE = 
RANLIB = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../../build/compiler/gcc-4.9.4_thumb_linux/usr/bin/arm-buildroot-linux-uclibcgnueabi-ranlib
RC = 
REQUIRE_LIB_DEPS = yes
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
SSL_BACKENDS = 
SSL_ENABLED = 
SSL_LIBS = 
STRIP = arm-linux-strip
SUPPORT_FEATURES = AsynchDNS Largefile UnixSockets alt-svc threadsafe
SUPPORT_PROTOCOLS = DICT FILE FTP GOPHER HTTP MQTT
USE_ARES = 
USE_BEARSSL = 
USE_GNUTLS = 
USE_HYPER = 
USE_LIBRTMP = 
USE_LIBSSH = 
USE_LIBSSH2 = 
USE_MBEDTLS = 
USE_MSH3 = 
USE_NGHTTP2 = 
USE_NGHTTP3 = 
USE_NGTCP2 = 
USE_NGTCP2_CRYPTO_GNUTLS = 
USE_NGTCP2_CRYPTO_OPENSSL = 
USE_NGTCP2_CRYPTO_WOLFSSL = 
USE_NSS = 
USE_OPENLDAP = 
USE_QUICHE = 
USE_RUSTLS = 
USE_SCHANNEL = 
USE_SECTRANSP = 
USE_UNIX_SOCKETS = 1
USE_WIN32_CRYPTO = 
USE_WIN32_LARGE_FILES = 
USE_WIN32_SMALL_FILES = 
USE_WINDOWS_SSPI = 
USE_WOLFSSH = 
USE_WOLFSSL = 
VERSION = -
VERSIONNUM = 075600
ZLIB_LIBS = 
ZSH_FUNCTIONS_DIR = ${prefix}/share/zsh/site-functions
abs_builddir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/docs
abs_srcdir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/../curl-7.86.0/docs
abs_top_builddir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build
abs_top_srcdir = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/build/../curl-7.86.0
ac_ct_AR = 
ac_ct_CC = 
ac_ct_DUMPBIN = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-uclibc
build_alias = 
build_cpu = x86_64
build_os = linux-uclibc
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = arm-unknown-linux-gnu
host_alias = arm-linux
host_cpu = arm
host_os = linux-gnu
host_vendor = unknown
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/lib/libcurl/curl-7.86.0/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
libext = a
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/project/zx297520v3/prj_mifi_min_aic8800dw/build/../../../../lib/libcurl/install
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = ../../curl-7.86.0/docs
sysconfdir = ${prefix}/etc
target_alias = arm-linux
top_build_prefix = ../
top_builddir = ..
top_srcdir = ../../curl-7.86.0
AUTOMAKE_OPTIONS = foreign no-dependencies

# EXTRA_DIST breaks with $(abs_builddir) so build it using this variable
# but distribute it (using the relative file name) in the next variable
man_MANS = $(abs_builddir)/curl.1
noinst_man_MANS = curl.1 mk-ca-bundle.1
dist_man_MANS = curl-config.1
GENHTMLPAGES = curl.html curl-config.html mk-ca-bundle.html
PDFPAGES = curl.pdf curl-config.pdf mk-ca-bundle.pdf
MANDISTPAGES = curl.1.dist curl-config.1.dist
HTMLPAGES = $(GENHTMLPAGES)

# Build targets in this file (.) before cmdline-opts to ensure that
# the curl.1 rule below runs first
SUBDIRS = . cmdline-opts
DIST_SUBDIRS = $(SUBDIRS) examples libcurl
CLEANFILES = $(GENHTMLPAGES) $(PDFPAGES) $(MANDISTPAGES) curl.1
EXTRA_DIST = \
 $(noinst_man_MANS)                             \
 ALTSVC.md                                      \
 BINDINGS.md                                    \
 BUFREF.md                                      \
 BUG-BOUNTY.md                                  \
 BUGS.md                                        \
 CHECKSRC.md                                    \
 CIPHERS.md                                     \
 CMakeLists.txt                                 \
 CODE_OF_CONDUCT.md                             \
 CODE_REVIEW.md                                 \
 CODE_STYLE.md                                  \
 CONTRIBUTE.md                                  \
 CURL-DISABLE.md                                \
 DEPRECATE.md                                   \
 DYNBUF.md                                      \
 EXPERIMENTAL.md                                \
 FAQ                                            \
 FEATURES.md                                    \
 GOVERNANCE.md                                  \
 HELP-US.md                                     \
 HISTORY.md                                     \
 HSTS.md                                        \
 HTTP-COOKIES.md                                \
 HTTP2.md                                       \
 HTTP3.md                                       \
 HYPER.md                                       \
 INSTALL                                        \
 INSTALL.cmake                                  \
 INSTALL.md                                     \
 INTERNALS.md                                   \
 KNOWN_BUGS                                     \
 MAIL-ETIQUETTE                                 \
 MQTT.md                                        \
 NEW-PROTOCOL.md                                \
 options-in-versions                            \
 PARALLEL-TRANSFERS.md                          \
 README.md                                      \
 RELEASE-PROCEDURE.md                           \
 RUSTLS.md                                      \
 ROADMAP.md                                     \
 SECURITY-PROCESS.md                            \
 SSL-PROBLEMS.md                                \
 SSLCERTS.md                                    \
 THANKS                                         \
 TODO                                           \
 TheArtOfHttpScripting.md                       \
 URL-SYNTAX.md                                  \
 VERSIONS.md                                    \
 WEBSOCKET.md

MAN2HTML = roffit $< >$@
SUFFIXES = .1 .html .pdf
all: all-recursive

.SUFFIXES:
.SUFFIXES: .1 .html .pdf
$(srcdir)/Makefile.in: # $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign docs/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign docs/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: # $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): # $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-man1: $(dist_man_MANS) $(man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(dist_man_MANS) $(man_MANS)'; \
	test -n "$(man1dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man1dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man1dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.1[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^1][0-9a-z]*$$,1,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man1dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man1dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man1dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man1dir)" || exit $$?; }; \
	done; }

uninstall-man1:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man1dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(dist_man_MANS) $(man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.1[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^1][0-9a-z]*$$,1,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man1dir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-recursive
all-am: Makefile $(MANS)
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(man1dir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-generic clean-libtool mostlyclean-am

distclean-am: clean-am distclean-generic distclean-tags

dvi: dvi-recursive

dvi-am:

html-am:

info: info-recursive

info-am:

install-data-am: install-man

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am:

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man: install-man1

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-man

uninstall-man: uninstall-man1

.MAKE: $(am__recursive_targets) install-am install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am check \
	check-am clean clean-generic clean-libtool cscopelist-am ctags \
	ctags-am distclean distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-man1 install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-man uninstall-man1

.PRECIOUS: Makefile


# $(abs_builddir) is to disable VPATH when searching for this file, which
# would otherwise find the copy in $(srcdir) which breaks the $(HUGE)
# rule in src/Makefile.am in out-of-tree builds that references the file in the
# build directory.
#
# First, seed the used copy of curl.1 with the prebuilt copy (in an out-of-tree
# build), then run make recursively to rebuild it only if its dependencies
# have changed.
$(abs_builddir)/curl.1:
	if test "$(top_builddir)x" != "$(top_srcdir)x" -a -e "$(srcdir)/curl.1"; then \
		$(INSTALL_DATA) "$(srcdir)/curl.1" $@; fi
	cd cmdline-opts && $(MAKE)

html: $(HTMLPAGES)
	cd libcurl && $(MAKE) html

pdf: $(PDFPAGES)
	cd libcurl && $(MAKE) pdf

.1.html:
	$(MAN2HTML)

.1.pdf:
	@(foo=`echo $@ | sed -e 's/\.[0-9]$$//g'`; \
	groff -Tps -man $< >$$foo.ps; \
	ps2pdf $$foo.ps $@; \
	rm $$foo.ps; \
	echo "converted $< to $@")

distclean:
	rm -f $(CLEANFILES)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
