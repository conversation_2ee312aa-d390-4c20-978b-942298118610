.\" **************************************************************************
.\" *                                  _   _ ____  _
.\" *  Project                     ___| | | |  _ \| |
.\" *                             / __| | | | |_) | |
.\" *                            | (__| |_| |  _ <| |___
.\" *                             \___|\___/|_| \_\_____|
.\" *
.\" * Copyright (C) 1998 \- 2022, <PERSON>, <<EMAIL>>, et al.
.\" *
.\" * This software is licensed as described in the file COPYING, which
.\" * you should have received as part of this distribution. The terms
.\" * are also available at https://curl.se/docs/copyright.html.
.\" *
.\" * You may opt to use, copy, modify, merge, publish, distribute and/or sell
.\" * copies of the Software, and permit persons to whom the Software is
.\" * furnished to do so, under the terms of the COPYING file.
.\" *
.\" * This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
.\" * KIND, either express or implied.
.\" *
.\" * SPDX-License-Identifier: curl
.\" *
.\" **************************************************************************
.\"
.\" DO NOT EDIT. Generated by the curl project gen.pl man page generator.
.\"
.TH curl 1 "October 23 2022" "curl 7.86.0" "curl Manual"
.SH NAME
curl \- transfer a URL
.SH SYNOPSIS
.B curl [options / URLs]
.SH DESCRIPTION
\fBcurl\fP is a tool for transferring data from or to a server. It supports these
protocols: DICT, FILE, FTP, FTPS, GOPHER, GOPHERS, HTTP, HTTPS, IMAP, IMAPS,
LDAP, LDAPS, MQTT, POP3, POP3S, RTMP, RTMPS, RTSP, SCP, SFTP, SMB, SMBS, SMTP,
SMTPS, TELNET, TFTP, WS and WSS. The command is designed to work without user
interaction.

curl offers a busload of useful tricks like proxy support, user
authentication, FTP upload, HTTP post, SSL connections, cookies, file transfer
resume and more. As you will see below, the number of features will make your
head spin.

curl is powered by libcurl for all transfer-related features. See
\fIlibcurl(3)\fP for details.
.SH URL
The URL syntax is protocol-dependent. You find a detailed description in
RFC 3986.

You can specify multiple URLs or parts of URLs by writing part sets within
braces and quoting the URL as in:

.nf
  \(dqhttp://site.{one,two,three}.com"
.fi

or you can get sequences of alphanumeric series by using [] as in:

.nf
  \(dqftp://ftp.example.com/file[1-100].txt"
.fi

.nf
  \(dqftp://ftp.example.com/file[001-100].txt"    (with leading zeros)
.fi

.nf
  \(dqftp://ftp.example.com/file[a-z].txt"
.fi

Nested sequences are not supported, but you can use several ones next to each
other:

.nf
  \(dqhttp://example.com/archive[1996-1999]/vol[1-4]/part{a,b,c}.html"
.fi

You can specify any amount of URLs on the command line. They will be fetched
in a sequential manner in the specified order. You can specify command line
options and URLs mixed and in any order on the command line.

You can specify a step counter for the ranges to get every Nth number or
letter:

.nf
  \(dqhttp://example.com/file[1-100:10].txt"
.fi

.nf
  \(dqhttp://example.com/file[a-z:2].txt"
.fi

When using [] or {} sequences when invoked from a command line prompt, you
probably have to put the full URL within double quotes to avoid the shell from
interfering with it. This also goes for other characters treated special, like
for example '&', '?' and '*'.

Provide the IPv6 zone index in the URL with an escaped percentage sign and the
interface name. Like in

.nf
  \(dqhttp://[fe80::3%25eth0]/"
.fi

If you specify URL without protocol:// prefix, curl will attempt to guess what
protocol you might want. It will then default to HTTP but try other protocols
based on often-used host name prefixes. For example, for host names starting
with "ftp." curl will assume you want to speak FTP.

curl will do its best to use what you pass to it as a URL. It is not trying to
validate it as a syntactically correct URL by any means but is fairly liberal
with what it accepts.

curl will attempt to re-use connections for multiple file transfers, so that
getting many files from the same server will not do multiple connects /
handshakes. This improves speed. Of course this is only done on files
specified on a single command line and cannot be used between separate curl
invocations.
.SH OUTPUT
If not told otherwise, curl writes the received data to stdout. It can be
instructed to instead save that data into a local file, using the \-\-output or
\-\-remote-name options. If curl is given multiple URLs to transfer on the
command line, it similarly needs multiple options for where to save them.

curl does not parse or otherwise "understand" the content it gets or writes as
output. It does no encoding or decoding, unless explicitly asked to with
dedicated command line options.
.SH PROTOCOLS
curl supports numerous protocols, or put in URL terms: schemes. Your
particular build may not support them all.
.IP DICT
Lets you lookup words using online dictionaries.
.IP FILE
Read or write local files. curl does not support accessing file:// URL
remotely, but when running on Microsoft Windows using the native UNC approach
will work.
.IP FTP(S)
curl supports the File Transfer Protocol with a lot of tweaks and levers. With
or without using TLS.
.IP GOPHER(S)
Retrieve files.
.IP HTTP(S)
curl supports HTTP with numerous options and variations. It can speak HTTP
version 0.9, 1.0, 1.1, 2 and 3 depending on build options and the correct
command line options.
.IP IMAP(S)
Using the mail reading protocol, curl can "download" emails for you. With or
without using TLS.
.IP LDAP(S)
curl can do directory lookups for you, with or without TLS.
.IP MQTT
curl supports MQTT version 3. Downloading over MQTT equals "subscribe" to a
topic while uploading/posting equals "publish" on a topic. MQTT over TLS is
not supported (yet).
.IP POP3(S)
Downloading from a pop3 server means getting a mail. With or without using
TLS.
.IP RTMP(S)
The Realtime Messaging Protocol is primarily used to server streaming media
and curl can download it.
.IP RTSP
curl supports RTSP 1.0 downloads.
.IP SCP
curl supports SSH version 2 scp transfers.
.IP SFTP
curl supports SFTP (draft 5) done over SSH version 2.
.IP SMB(S)
curl supports SMB version 1 for upload and download.
.IP SMTP(S)
Uploading contents to an SMTP server means sending an email. With or without
TLS.
.IP TELNET
Telling curl to fetch a telnet URL starts an interactive session where it
sends what it reads on stdin and outputs what the server sends it.
.IP TFTP
curl can do TFTP downloads and uploads.
.SH "PROGRESS METER"
curl normally displays a progress meter during operations, indicating the
amount of transferred data, transfer speeds and estimated time left, etc. The
progress meter displays number of bytes and the speeds are in bytes per
second. The suffixes (k, M, G, T, P) are 1024 based. For example 1k is 1024
bytes. 1M is 1048576 bytes.

curl displays this data to the terminal by default, so if you invoke curl to
do an operation and it is about to write data to the terminal, it
\fIdisables\fP the progress meter as otherwise it would mess up the output
mixing progress meter and response data.

If you want a progress meter for HTTP POST or PUT requests, you need to
redirect the response output to a file, using shell redirect (>), \-\-output or
similar.

This does not apply to FTP upload as that operation does not spit out any
response data to the terminal.

If you prefer a progress "bar" instead of the regular meter, \-\-progress-bar is
your friend. You can also disable the progress meter completely with the
\-\-silent option.
.SH OPTIONS
Options start with one or two dashes. Many of the options require an
additional value next to them.

The short "single-dash" form of the options, \-d for example, may be used with
or without a space between it and its value, although a space is a recommended
separator. The long "double-dash" form, \-\-data for example, requires a space
between it and its value.

Short version options that do not need any additional values can be used
immediately next to each other, like for example you can specify all the
options \-O, \-L and \-v at once as \-OLv.

In general, all boolean options are enabled with \-\-\fBoption\fP and yet again
disabled with \-\-\fBno-\fPoption. That is, you use the same option name but
prefix it with "no-". However, in this list we mostly only list and show the
\-\-option version of them.
.IP "\-\-abstract-unix-socket <path>"
(HTTP) Connect through an abstract Unix domain socket, instead of using the network.
Note: netstat shows the path of an abstract socket prefixed with '@', however
the <path> argument should not have this leading character.

If --abstract-unix-socket is provided several times, the last set value will be used.

Example:
.nf
 curl --abstract-unix-socket socketpath https://example.com
.fi

See also \fI--unix-socket\fP. Added in 7.53.0.
.IP "\-\-alt-svc <file name>"
(HTTPS) This option enables the alt-svc parser in curl. If the file name points to an
existing alt-svc cache file, that will be used. After a completed transfer,
the cache will be saved to the file name again if it has been modified.

Specify a "" file name (zero length) to avoid loading/saving and make curl
just handle the cache in memory.

If this option is used several times, curl will load contents from all the
files but the last one will be used for saving.

--alt-svc can be used several times in a command line

Example:
.nf
 curl --alt-svc svc.txt https://example.com
.fi

See also \fI--resolve\fP and \fI--connect-to\fP. Added in 7.64.1.
.IP "\-\-anyauth"
(HTTP) Tells curl to figure out authentication method by itself, and use the most
secure one the remote site claims to support. This is done by first doing a
request and checking the response-headers, thus possibly inducing an extra
network round-trip. This is used instead of setting a specific authentication
method, which you can do with \fI\-\-basic\fP, \fI\-\-digest\fP, \fI\-\-ntlm\fP, and \fI\-\-negotiate\fP.

Using \-\-anyauth is not recommended if you do uploads from stdin, since it may
require data to be sent twice and then the client must be able to rewind. If
the need should arise when uploading from stdin, the upload operation will
fail.

Used together with \fI\-u, \-\-user\fP.

Providing --anyauth multiple times has no extra effect.

Example:
.nf
 curl --anyauth --user me:pwd https://example.com
.fi

See also \fI--proxy-anyauth\fP, \fI--basic\fP and \fI--digest\fP.
.IP "\-a, \-\-append"
(FTP SFTP) When used in an upload, this makes curl append to the target file instead of
overwriting it. If the remote file does not exist, it will be created. Note
that this flag is ignored by some SFTP servers (including OpenSSH).

Providing --append multiple times has no extra effect.
Disable it again with --no-append.

Example:
.nf
 curl --upload-file local --append ftp://example.com/
.fi

See also \fI-r, --range\fP and \fI-C, --continue-at\fP.
.IP "\-\-aws-sigv4 <provider1[:provider2[:region[:service]]]>"
Use AWS V4 signature authentication in the transfer.

The provider argument is a string that is used by the algorithm when creating
outgoing authentication headers.

The region argument is a string that points to a geographic area of
a resources collection (region-code) when the region name is omitted from
the endpoint.

The service argument is a string that points to a function provided by a cloud
(service-code) when the service name is omitted from the endpoint.

If --aws-sigv4 is provided several times, the last set value will be used.

Example:
.nf
 curl --aws-sigv4 "aws:amz:east-2:es" --user "key:secret" https://example.com
.fi

See also \fI--basic\fP and \fI-u, --user\fP. Added in 7.75.0.
.IP "\-\-basic"
(HTTP) Tells curl to use HTTP Basic authentication with the remote host. This is the
default and this option is usually pointless, unless you use it to override a
previously set option that sets a different authentication method (such as
\fI\-\-ntlm\fP, \fI\-\-digest\fP, or \fI\-\-negotiate\fP).

Used together with \fI\-u, \-\-user\fP.

Providing --basic multiple times has no extra effect.

Example:
.nf
 curl -u name:password --basic https://example.com
.fi

See also \fI--proxy-basic\fP.
.IP "\-\-cacert <file>"
(TLS) Tells curl to use the specified certificate file to verify the peer. The file
may contain multiple CA certificates. The certificate(s) must be in PEM
format. Normally curl is built to use a default file for this, so this option
is typically used to alter that default file.

curl recognizes the environment variable named 'CURL_CA_BUNDLE' if it is
set, and uses the given path as a path to a CA cert bundle. This option
overrides that variable.

The windows version of curl will automatically look for a CA certs file named
\(aqcurl-ca-bundle.crt', either in the same directory as curl.exe, or in the
Current Working Directory, or in any folder along your PATH.

If curl is built against the NSS SSL library, the NSS PEM PKCS#11 module
(libnsspem.so) needs to be available for this option to work properly.

(iOS and macOS only) If curl is built against Secure Transport, then this
option is supported for backward compatibility with other SSL engines, but it
should not be set. If the option is not set, then curl will use the
certificates in the system and user Keychain to verify the peer, which is the
preferred method of verifying the peer's certificate chain.

(Schannel only) This option is supported for Schannel in Windows 7 or later
with libcurl 7.60 or later. This option is supported for backward
compatibility with other SSL engines; instead it is recommended to use
Windows' store of root certificates (the default for Schannel).

If --cacert is provided several times, the last set value will be used.

Example:
.nf
 curl --cacert CA-file.txt https://example.com
.fi

See also \fI--capath\fP and \fI-k, --insecure\fP.
.IP "\-\-capath <dir>"
(TLS) Tells curl to use the specified certificate directory to verify the
peer. Multiple paths can be provided by separating them with ":" (e.g.
\(dqpath1:path2:path3"). The certificates must be in PEM format, and if curl is
built against OpenSSL, the directory must have been processed using the
c_rehash utility supplied with OpenSSL. Using \-\-capath can allow
OpenSSL-powered curl to make SSL-connections much more efficiently than using
\-\-cacert if the \-\-cacert file contains many CA certificates.

If this option is set, the default capath value will be ignored.

If --capath is provided several times, the last set value will be used.

Example:
.nf
 curl --capath /local/directory https://example.com
.fi

See also \fI--cacert\fP and \fI-k, --insecure\fP.
.IP "\-\-cert-status"
(TLS) Tells curl to verify the status of the server certificate by using the
Certificate Status Request (aka. OCSP stapling) TLS extension.

If this option is enabled and the server sends an invalid (e.g. expired)
response, if the response suggests that the server certificate has been
revoked, or no response at all is received, the verification fails.

This is currently only implemented in the OpenSSL, GnuTLS and NSS backends.

Providing --cert-status multiple times has no extra effect.
Disable it again with --no-cert-status.

Example:
.nf
 curl --cert-status https://example.com
.fi

See also \fI--pinnedpubkey\fP. Added in 7.41.0.
.IP "\-\-cert-type <type>"
(TLS) Tells curl what type the provided client certificate is using. PEM, DER, ENG
and P12 are recognized types.

The default type depends on the TLS backend and is usually PEM, however for
Secure Transport and Schannel it is P12. If \-\-cert is a pkcs11: URI then ENG is
the default type.

If --cert-type is provided several times, the last set value will be used.

Example:
.nf
 curl --cert-type PEM --cert file https://example.com
.fi

See also \fI-E, --cert\fP, \fI--key\fP and \fI--key-type\fP.
.IP "\-E, \-\-cert <certificate[:password]>"
(TLS) Tells curl to use the specified client certificate file when getting a file
with HTTPS, FTPS or another SSL-based protocol. The certificate must be in
PKCS#12 format if using Secure Transport, or PEM format if using any other
engine. If the optional password is not specified, it will be queried for on
the terminal. Note that this option assumes a certificate file that is the
private key and the client certificate concatenated. See \-\-cert and \-\-key to
specify them independently.

In the <certificate> portion of the argument, you must escape the character ":"
as "\\:" so that it is not recognized as the password delimiter. Similarly, you
must escape the character "\\" as "\\\\" so that it is not recognized as an
escape character.

If curl is built against the NSS SSL library then this option can tell
curl the nickname of the certificate to use within the NSS database defined
by the environment variable SSL_DIR (or by default /etc/pki/nssdb). If the
NSS PEM PKCS#11 module (libnsspem.so) is available then PEM files may be
loaded.

If you provide a path relative to the current directory, you must prefix the
path with "./" in order to avoid confusion with an NSS database nickname.

If curl is built against OpenSSL library, and the engine pkcs11 is available,
then a PKCS#11 URI (RFC 7512) can be used to specify a certificate located in
a PKCS#11 device. A string beginning with "pkcs11:" will be interpreted as a
PKCS#11 URI. If a PKCS#11 URI is provided, then the \-\-engine option will be set
as "pkcs11" if none was provided and the \-\-cert-type option will be set as
\(dqENG" if none was provided.

(iOS and macOS only) If curl is built against Secure Transport, then the
certificate string can either be the name of a certificate/private key in the
system or user keychain, or the path to a PKCS#12-encoded certificate and
private key. If you want to use a file from the current directory, please
precede it with "./" prefix, in order to avoid confusion with a nickname.

(Schannel only) Client certificates must be specified by a path
expression to a certificate store. (Loading PFX is not supported; you can
import it to a store first). You can use
\(dq<store location>\\<store name>\\<thumbprint>" to refer to a certificate
in the system certificates store, for example,
\(dqCurrentUser\\MY\\934a7ac6f8a5d579285a74fa61e19f23ddfe8d7a". Thumbprint is
usually a SHA-1 hex string which you can see in certificate details. Following
store locations are supported: CurrentUser, LocalMachine, CurrentService,
Services, CurrentUserGroupPolicy, LocalMachineGroupPolicy,
LocalMachineEnterprise.

If --cert is provided several times, the last set value will be used.

Example:
.nf
 curl --cert certfile --key keyfile https://example.com
.fi

See also \fI--cert-type\fP, \fI--key\fP and \fI--key-type\fP.
.IP "\-\-ciphers <list of ciphers>"
(TLS) Specifies which ciphers to use in the connection. The list of ciphers must
specify valid ciphers. Read up on SSL cipher list details on this URL:

.nf
 https://curl.se/docs/ssl-ciphers.html
.fi

If --ciphers is provided several times, the last set value will be used.

Example:
.nf
 curl --ciphers ECDHE-ECDSA-AES256-CCM8 https://example.com
.fi

See also \fI--tlsv1.3\fP.
.IP "\-\-compressed-ssh"
(SCP SFTP) Enables built-in SSH compression.
This is a request, not an order; the server may or may not do it.

Providing --compressed-ssh multiple times has no extra effect.
Disable it again with --no-compressed-ssh.

Example:
.nf
 curl --compressed-ssh sftp://example.com/
.fi

See also \fI--compressed\fP. Added in 7.56.0.
.IP "\-\-compressed"
(HTTP) Request a compressed response using one of the algorithms curl supports, and
automatically decompress the content. Headers are not modified.

If this option is used and the server sends an unsupported encoding, curl will
report an error. This is a request, not an order; the server may or may not
deliver data compressed.

Providing --compressed multiple times has no extra effect.
Disable it again with --no-compressed.

Example:
.nf
 curl --compressed https://example.com
.fi

See also \fI--compressed-ssh\fP.
.IP "\-K, \-\-config <file>"
Specify a text file to read curl arguments from. The command line arguments
found in the text file will be used as if they were provided on the command
line.

Options and their parameters must be specified on the same line in the file,
separated by whitespace, colon, or the equals sign. Long option names can
optionally be given in the config file without the initial double dashes and
if so, the colon or equals characters can be used as separators. If the option
is specified with one or two dashes, there can be no colon or equals character
between the option and its parameter.

If the parameter contains whitespace (or starts with : or =), the parameter
must be enclosed within quotes. Within double quotes, the following escape
sequences are available: \\\\, \\", \\t, \\n, \\r and \\v. A backslash
preceding any other letter is ignored.

If the first column of a config line is a '#' character, the rest of the line
will be treated as a comment.

Only write one option per physical line in the config file.

Specify the filename to \-\-config as '-' to make curl read the file from stdin.

Note that to be able to specify a URL in the config file, you need to specify
it using the \-\-url option, and not by simply writing the URL on its own
line. So, it could look similar to this:

url = "https://curl.se/docs/"

.nf
 # \-\-\- Example file \-\-\-
 # this is a comment
 url = "example.com"
 output = "curlhere.html"
 user-agent = "superagent/1.0"
.fi

.nf
 # and fetch another URL too
 url = "example.com/docs/manpage.html"
 \-O
 referer = "http://nowhereatall.example.com/"
 # \-\-\- End of example file \-\-\-
.fi

When curl is invoked, it (unless \-\-disable is used) checks for a default
config file and uses it if found, even when \-\-config is used. The default
config file is checked for in the following places in this order:

1) "$CURL_HOME/.curlrc"

2) "$XDG_CONFIG_HOME/.curlrc" (Added in 7.73.0)

3) "$HOME/.curlrc"

4) Windows: "%USERPROFILE%\\.curlrc"

5) Windows: "%APPDATA%\\.curlrc"

6) Windows: "%USERPROFILE%\\Application Data\\.curlrc"

7) Non-Windows: use getpwuid to find the home directory

8) On Windows, if it finds no .curlrc file in the sequence described above, it
checks for one in the same dir the curl executable is placed.

On Windows two filenames are checked per location: .curlrc and _curlrc,
preferring the former. Older versions on Windows checked for _curlrc only.

--config can be used several times in a command line

Example:
.nf
 curl --config file.txt https://example.com
.fi

See also \fI-q, --disable\fP.
.IP "\-\-connect-timeout <fractional seconds>"
Maximum time in seconds that you allow curl's connection to take.  This only
limits the connection phase, so if curl connects within the given period it
will continue \- if not it will exit.  Since version 7.32.0, this option
accepts decimal values.

If --connect-timeout is provided several times, the last set value will be used.

Examples:
.nf
 curl --connect-timeout 20 https://example.com
 curl --connect-timeout 3.14 https://example.com
.fi

See also \fI-m, --max-time\fP.
.IP "\-\-connect-to <HOST1:PORT1:HOST2:PORT2>"

For a request to the given HOST1:PORT1 pair, connect to HOST2:PORT2 instead.
This option is suitable to direct requests at a specific server, e.g. at a
specific cluster node in a cluster of servers. This option is only used to
establish the network connection. It does NOT affect the hostname/port that is
used for TLS/SSL (e.g. SNI, certificate verification) or for the application
protocols. "HOST1" and "PORT1" may be the empty string, meaning "any
host/port". "HOST2" and "PORT2" may also be the empty string, meaning "use the
request's original host/port".

A "host" specified to this option is compared as a string, so it needs to
match the name used in request URL. It can be either numerical such as
\(dq127.0.0.1" or the full host name such as "example.org".

--connect-to can be used several times in a command line

Example:
.nf
 curl --connect-to example.com:443:example.net:8443 https://example.com
.fi

See also \fI--resolve\fP and \fI-H, --header\fP. Added in 7.49.0.
.IP "\-C, \-\-continue-at <offset>"
Continue/Resume a previous file transfer at the given offset. The given offset
is the exact number of bytes that will be skipped, counting from the beginning
of the source file before it is transferred to the destination. If used with
uploads, the FTP server command SIZE will not be used by curl.

Use "-C \-" to tell curl to automatically find out where/how to resume the
transfer. It then uses the given output/input files to figure that out.

If --continue-at is provided several times, the last set value will be used.

Examples:
.nf
 curl -C - https://example.com
 curl -C 400 https://example.com
.fi

See also \fI-r, --range\fP.
.IP "\-c, \-\-cookie-jar <filename>"
(HTTP) Specify to which file you want curl to write all cookies after a completed
operation. Curl writes all cookies from its in-memory cookie storage to the
given file at the end of operations. If no cookies are known, no data will be
written. The file will be written using the Netscape cookie file format. If
you set the file name to a single dash, "-", the cookies will be written to
stdout.

This command line option will activate the cookie engine that makes curl
record and use cookies. Another way to activate it is to use the \-\-cookie
option.

If the cookie jar cannot be created or written to, the whole curl operation
will not fail or even report an error clearly. Using \-\-verbose will get a
warning displayed, but that is the only visible feedback you get about this
possibly lethal situation.

If --cookie-jar is provided several times, the last set value will be used.

Examples:
.nf
 curl -c store-here.txt https://example.com
 curl -c store-here.txt -b read-these https://example.com
.fi

See also \fI-b, --cookie\fP.
.IP "\-b, \-\-cookie <data|filename>"
(HTTP) Pass the data to the HTTP server in the Cookie header. It is supposedly the
data previously received from the server in a "Set-Cookie:" line. The data
should be in the format "NAME1=VALUE1; NAME2=VALUE2". This makes curl use the
cookie header with this content explicitly in all outgoing request(s). If
multiple requests are done due to authentication, followed redirects or
similar, they will all get this cookie passed on.

If no '=' symbol is used in the argument, it is instead treated as a filename
to read previously stored cookie from. This option also activates the cookie
engine which will make curl record incoming cookies, which may be handy if
you are using this in combination with the \-\-location option or do multiple URL
transfers on the same invoke. If the file name is exactly a minus ("-"), curl
will instead read the contents from stdin.

The file format of the file to read cookies from should be plain HTTP headers
(Set-Cookie style) or the Netscape/Mozilla cookie file format.

The file specified with \-\-cookie is only used as input. No cookies will be
written to the file. To store cookies, use the \-\-cookie-jar option.

If you use the Set-Cookie file format and do not specify a domain then the
cookie is not sent since the domain will never match. To address this, set a
domain in Set-Cookie line (doing that will include sub-domains) or preferably:
use the Netscape format.

Users often want to both read cookies from a file and write updated cookies
back to a file, so using both \-\-cookie and \-\-cookie-jar in the same command
line is common.

--cookie can be used several times in a command line

Examples:
.nf
 curl -b cookiefile https://example.com
 curl -b cookiefile -c cookiefile https://example.com
.fi

See also \fI-c, --cookie-jar\fP and \fI-j, --junk-session-cookies\fP.
.IP "\-\-create-dirs"
When used in conjunction with the \-\-output option, curl will create the
necessary local directory hierarchy as needed. This option creates the
directories mentioned with the \-\-output option, nothing else. If the \-\-output
file name uses no directory, or if the directories it mentions already exist,
no directories will be created.

Created dirs are made with mode 0750 on unix style file systems.

To create remote directories when using FTP or SFTP, try \fI\-\-ftp-create-dirs\fP.

Providing --create-dirs multiple times has no extra effect.
Disable it again with --no-create-dirs.

Example:
.nf
 curl --create-dirs --output local/dir/file https://example.com
.fi

See also \fI--ftp-create-dirs\fP and \fI--output-dir\fP.
.IP "\-\-create-file-mode <mode>"
(SFTP SCP FILE) When curl is used to create files remotely using one of the supported
protocols, this option allows the user to set which 'mode' to set on the file
at creation time, instead of the default 0644.

This option takes an octal number as argument.

If --create-file-mode is provided several times, the last set value will be used.

Example:
.nf
 curl --create-file-mode 0777 -T localfile sftp://example.com/new
.fi

See also \fI--ftp-create-dirs\fP. Added in 7.75.0.
.IP "\-\-crlf"
(FTP SMTP) Convert LF to CRLF in upload. Useful for MVS (OS/390).

(SMTP added in 7.40.0)

Providing --crlf multiple times has no extra effect.
Disable it again with --no-crlf.

Example:
.nf
 curl --crlf -T file ftp://example.com/
.fi

See also \fI-B, --use-ascii\fP.
.IP "\-\-crlfile <file>"
(TLS) Provide a file using PEM format with a Certificate Revocation List that may
specify peer certificates that are to be considered revoked.

If --crlfile is provided several times, the last set value will be used.

Example:
.nf
 curl --crlfile rejects.txt https://example.com
.fi

See also \fI--cacert\fP and \fI--capath\fP.
.IP "\-\-curves <algorithm list>"
(TLS) Tells curl to request specific curves to use during SSL session establishment
according to RFC 8422, 5.1.  Multiple algorithms can be provided by separating
them with ":" (e.g.  "X25519:P-521").  The parameter is available identically
in the "openssl s_client/s_server" utilities.

\-\-curves allows a OpenSSL powered curl to make SSL-connections with exactly
the (EC) curve requested by the client, avoiding nontransparent client/server
negotiations.

If this option is set, the default curves list built into openssl will be
ignored.

If --curves is provided several times, the last set value will be used.

Example:
.nf
 curl --curves X25519 https://example.com
.fi

See also \fI--ciphers\fP. Added in 7.73.0.
.IP "\-\-data-ascii <data>"
(HTTP) This is just an alias for \fI\-d, \-\-data\fP.

--data-ascii can be used several times in a command line

Example:
.nf
 curl --data-ascii @file https://example.com
.fi

See also \fI--data-binary\fP, \fI--data-raw\fP and \fI--data-urlencode\fP.
.IP "\-\-data-binary <data>"
(HTTP) This posts data exactly as specified with no extra processing whatsoever.

If you start the data with the letter @, the rest should be a filename. Data
is posted in a similar manner as \-\-data does, except that newlines and
carriage returns are preserved and conversions are never done.

Like \-\-data the default content-type sent to the server is
application/x-www-form-urlencoded. If you want the data to be treated as
arbitrary binary data by the server then set the content-type to octet-stream:
\-H "Content-Type: application/octet-stream".

If this option is used several times, the ones following the first will append
data as described in \fI\-d, \-\-data\fP.

--data-binary can be used several times in a command line

Example:
.nf
 curl --data-binary @filename https://example.com
.fi

See also \fI--data-ascii\fP.
.IP "\-\-data-raw <data>"
(HTTP) This posts data similarly to \-\-data but without the special
interpretation of the @ character.

--data-raw can be used several times in a command line

Examples:
.nf
 curl --data-raw "hello" https://example.com
 curl --data-raw "@at@at@" https://example.com
.fi

See also \fI-d, --data\fP. Added in 7.43.0.
.IP "\-\-data-urlencode <data>"
(HTTP) This posts data, similar to the other \-\-data options with the exception
that this performs URL-encoding.

To be CGI-compliant, the <data> part should begin with a \fIname\fP followed
by a separator and a content specification. The <data> part can be passed to
curl using one of the following syntaxes:
.RS
.IP "content"
This will make curl URL-encode the content and pass that on. Just be careful
so that the content does not contain any = or @ symbols, as that will then make
the syntax match one of the other cases below!
.IP "=content"
This will make curl URL-encode the content and pass that on. The preceding =
symbol is not included in the data.
.IP "name=content"
This will make curl URL-encode the content part and pass that on. Note that
the name part is expected to be URL-encoded already.
.IP "@filename"
This will make curl load data from the given file (including any newlines),
URL-encode that data and pass it on in the POST.
.IP "name@filename"
This will make curl load data from the given file (including any newlines),
URL-encode that data and pass it on in the POST. The name part gets an equal
sign appended, resulting in \fIname=urlencoded-file-content\fP. Note that the
name is expected to be URL-encoded already.
.RE

--data-urlencode can be used several times in a command line

Examples:
.nf
 curl --data-urlencode name=val https://example.com
 curl --data-urlencode =encodethis https://example.com
 curl --data-urlencode name@file https://example.com
 curl --data-urlencode @fileonly https://example.com
.fi

See also \fI-d, --data\fP and \fI--data-raw\fP.
.IP "\-d, \-\-data <data>"
(HTTP MQTT) Sends the specified data in a POST request to the HTTP server, in the same way
that a browser does when a user has filled in an HTML form and presses the
submit button. This will cause curl to pass the data to the server using the
content-type application/x-www-form-urlencoded. Compare to \fI\-F, \-\-form\fP.

\-\-data-raw is almost the same but does not have a special interpretation of
the @ character. To post data purely binary, you should instead use the
\-\-data-binary option. To URL-encode the value of a form field you may use
\fI\-\-data-urlencode\fP.

If any of these options is used more than once on the same command line, the
data pieces specified will be merged with a separating &-symbol. Thus, using
\(aq-d name=daniel \-d skill=lousy' would generate a post chunk that looks like
\(aqname=daniel&skill=lousy'.

If you start the data with the letter @, the rest should be a file name to
read the data from, or \- if you want curl to read the data from stdin. Posting
data from a file named 'foobar' would thus be done with \fI\-d, \-\-data\fP @foobar. When
\-\-data is told to read from a file like that, carriage returns and newlines
will be stripped out. If you do not want the @ character to have a special
interpretation use \-\-data-raw instead.

--data can be used several times in a command line

Examples:
.nf
 curl -d "name=curl" https://example.com
 curl -d "name=curl" -d "tool=cmdline" https://example.com
 curl -d @filename https://example.com
.fi

See also \fI--data-binary\fP, \fI--data-urlencode\fP and \fI--data-raw\fP. This option is mutually exclusive to \fI-F, --form\fP and \fI-I, --head\fP and \fI-T, --upload-file\fP.
.IP "\-\-delegation <LEVEL>"
(GSS/kerberos) Set LEVEL to tell the server what it is allowed to delegate when it
comes to user credentials.
.RS
.IP "none"
Do not allow any delegation.
.IP "policy"
Delegates if and only if the OK-AS-DELEGATE flag is set in the Kerberos
service ticket, which is a matter of realm policy.
.IP "always"
Unconditionally allow the server to delegate.
.RE

If --delegation is provided several times, the last set value will be used.

Example:
.nf
 curl --delegation "none" https://example.com
.fi

See also \fI-k, --insecure\fP and \fI--ssl\fP.
.IP "\-\-digest"
(HTTP) Enables HTTP Digest authentication. This is an authentication scheme that
prevents the password from being sent over the wire in clear text. Use this in
combination with the normal \-\-user option to set user name and password.

Providing --digest multiple times has no extra effect.
Disable it again with --no-digest.

Example:
.nf
 curl -u name:password --digest https://example.com
.fi

See also \fI-u, --user\fP, \fI--proxy-digest\fP and \fI--anyauth\fP. This option is mutually exclusive to \fI--basic\fP and \fI--ntlm\fP and \fI--negotiate\fP.
.IP "\-\-disable-eprt"
(FTP) Tell curl to disable the use of the EPRT and LPRT commands when doing active
FTP transfers. Curl will normally always first attempt to use EPRT, then LPRT
before using PORT, but with this option, it will use PORT right away. EPRT and
LPRT are extensions to the original FTP protocol, and may not work on all
servers, but they enable more functionality in a better way than the
traditional PORT command.

\-\-eprt can be used to explicitly enable EPRT again and \-\-no-eprt is an alias
for \fI\-\-disable-eprt\fP.

If the server is accessed using IPv6, this option will have no effect as EPRT
is necessary then.

Disabling EPRT only changes the active behavior. If you want to switch to
passive mode you need to not use \-\-ftp-port or force it with \fI\-\-ftp-pasv\fP.

Providing --disable-eprt multiple times has no extra effect.
Disable it again with --no-disable-eprt.

Example:
.nf
 curl --disable-eprt ftp://example.com/
.fi

See also \fI--disable-epsv\fP and \fI-P, --ftp-port\fP.
.IP "\-\-disable-epsv"
(FTP) Tell curl to disable the use of the EPSV command when doing passive FTP
transfers. Curl will normally always first attempt to use EPSV before
PASV, but with this option, it will not try using EPSV.

\-\-epsv can be used to explicitly enable EPSV again and \-\-no-epsv is an alias
for \fI\-\-disable-epsv\fP.

If the server is an IPv6 host, this option will have no effect as EPSV is
necessary then.

Disabling EPSV only changes the passive behavior. If you want to switch to
active mode you need to use \fI\-P, \-\-ftp-port\fP.

Providing --disable-epsv multiple times has no extra effect.
Disable it again with --no-disable-epsv.

Example:
.nf
 curl --disable-epsv ftp://example.com/
.fi

See also \fI--disable-eprt\fP and \fI-P, --ftp-port\fP.
.IP "\-q, \-\-disable"
If used as the first parameter on the command line, the \fIcurlrc\fP config
file will not be read and used. See the \-\-config for details on the default
config file search path.

Providing --disable multiple times has no extra effect.
Disable it again with --no-disable.

Example:
.nf
 curl -q https://example.com
.fi

See also \fI-K, --config\fP.
.IP "\-\-disallow-username-in-url"
(HTTP) This tells curl to exit if passed a URL containing a username. This is probably
most useful when the URL is being provided at runtime or similar.

Providing --disallow-username-in-url multiple times has no extra effect.
Disable it again with --no-disallow-username-in-url.

Example:
.nf
 curl --disallow-username-in-url https://example.com
.fi

See also \fI--proto\fP. Added in 7.61.0.
.IP "\-\-dns-interface <interface>"
(DNS) Tell curl to send outgoing DNS requests through <interface>. This option is a
counterpart to \fI\-\-interface\fP (which does not affect DNS). The supplied string
must be an interface name (not an address).

If --dns-interface is provided several times, the last set value will be used.

Example:
.nf
 curl --dns-interface eth0 https://example.com
.fi

See also \fI--dns-ipv4-addr\fP and \fI--dns-ipv6-addr\fP. \fI--dns-interface\fP requires that the underlying libcurl was built to support c-ares. Added in 7.33.0.
.IP "\-\-dns-ipv4-addr <address>"
(DNS) Tell curl to bind to <ip-address> when making IPv4 DNS requests, so that
the DNS requests originate from this address. The argument should be a
single IPv4 address.

If --dns-ipv4-addr is provided several times, the last set value will be used.

Example:
.nf
 curl --dns-ipv4-addr ******** https://example.com
.fi

See also \fI--dns-interface\fP and \fI--dns-ipv6-addr\fP. \fI--dns-ipv4-addr\fP requires that the underlying libcurl was built to support c-ares. Added in 7.33.0.
.IP "\-\-dns-ipv6-addr <address>"
(DNS) Tell curl to bind to <ip-address> when making IPv6 DNS requests, so that
the DNS requests originate from this address. The argument should be a
single IPv6 address.

If --dns-ipv6-addr is provided several times, the last set value will be used.

Example:
.nf
 curl --dns-ipv6-addr 2a04:4e42::561 https://example.com
.fi

See also \fI--dns-interface\fP and \fI--dns-ipv4-addr\fP. \fI--dns-ipv6-addr\fP requires that the underlying libcurl was built to support c-ares. Added in 7.33.0.
.IP "\-\-dns-servers <addresses>"
Set the list of DNS servers to be used instead of the system default.
The list of IP addresses should be separated with commas. Port numbers
may also optionally be given as \fI:<port-number>\fP after each IP
address.

If --dns-servers is provided several times, the last set value will be used.

Example:
.nf
 curl --dns-servers ***********,*********** https://example.com
.fi

See also \fI--dns-interface\fP and \fI--dns-ipv4-addr\fP. \fI--dns-servers\fP requires that the underlying libcurl was built to support c-ares. Added in 7.33.0.
.IP "\-\-doh-cert-status"
Same as \-\-cert-status but used for DoH (DNS-over-HTTPS).

Providing --doh-cert-status multiple times has no extra effect.
Disable it again with --no-doh-cert-status.

Example:
.nf
 curl --doh-cert-status --doh-url https://doh.example https://example.com
.fi

See also \fI--doh-insecure\fP. Added in 7.76.0.
.IP "\-\-doh-insecure"
Same as \-\-insecure but used for DoH (DNS-over-HTTPS).

Providing --doh-insecure multiple times has no extra effect.
Disable it again with --no-doh-insecure.

Example:
.nf
 curl --doh-insecure --doh-url https://doh.example https://example.com
.fi

See also \fI--doh-url\fP. Added in 7.76.0.
.IP "\-\-doh-url <URL>"
Specifies which DNS-over-HTTPS (DoH) server to use to resolve hostnames,
instead of using the default name resolver mechanism. The URL must be HTTPS.

Some SSL options that you set for your transfer will apply to DoH since the
name lookups take place over SSL. However, the certificate verification
settings are not inherited and can be controlled separately via
\-\-doh-insecure and \fI\-\-doh-cert-status\fP.

This option is unset if an empty string "" is used as the URL. (Added in
7.85.0)

If --doh-url is provided several times, the last set value will be used.

Example:
.nf
 curl --doh-url https://doh.example https://example.com
.fi

See also \fI--doh-insecure\fP. Added in 7.62.0.
.IP "\-D, \-\-dump-header <filename>"
(HTTP FTP) Write the received protocol headers to the specified file. If no headers are
received, the use of this option will create an empty file.

When used in FTP, the FTP server response lines are considered being "headers"
and thus are saved there.

If --dump-header is provided several times, the last set value will be used.

Example:
.nf
 curl --dump-header store.txt https://example.com
.fi

See also \fI-o, --output\fP.
.IP "\-\-egd-file <file>"
(TLS) Deprecated option. This option is ignored by curl since 7.84.0. Prior to that
it only had an effect on curl if built to use old versions of OpenSSL.

Specify the path name to the Entropy Gathering Daemon socket. The socket is
used to seed the random engine for SSL connections.

If --egd-file is provided several times, the last set value will be used.

Example:
.nf
 curl --egd-file /random/here https://example.com
.fi

See also \fI--random-file\fP.
.IP "\-\-engine <name>"
(TLS) Select the OpenSSL crypto engine to use for cipher operations. Use \-\-engine
list to print a list of build-time supported engines. Note that not all (and
possibly none) of the engines may be available at runtime.

If --engine is provided several times, the last set value will be used.

Example:
.nf
 curl --engine flavor https://example.com
.fi

See also \fI--ciphers\fP and \fI--curves\fP.
.IP "\-\-etag-compare <file>"
(HTTP) This option makes a conditional HTTP request for the specific ETag read
from the given file by sending a custom If-None-Match header using the
stored ETag.

For correct results, make sure that the specified file contains only a
single line with the desired ETag. An empty file is parsed as an empty
ETag.

Use the option \-\-etag-save to first save the ETag from a response, and
then use this option to compare against the saved ETag in a subsequent
request.

If --etag-compare is provided several times, the last set value will be used.

Example:
.nf
 curl --etag-compare etag.txt https://example.com
.fi

See also \fI--etag-save\fP and \fI-z, --time-cond\fP. Added in 7.68.0.
.IP "\-\-etag-save <file>"
(HTTP) This option saves an HTTP ETag to the specified file. An ETag is a
caching related header, usually returned in a response.

If no ETag is sent by the server, an empty file is created.

If --etag-save is provided several times, the last set value will be used.

Example:
.nf
 curl --etag-save storetag.txt https://example.com
.fi

See also \fI--etag-compare\fP. Added in 7.68.0.
.IP "\-\-expect100-timeout <seconds>"
(HTTP) Maximum time in seconds that you allow curl to wait for a 100-continue
response when curl emits an Expects: 100-continue header in its request. By
default curl will wait one second. This option accepts decimal values! When
curl stops waiting, it will continue as if the response has been received.

If --expect100-timeout is provided several times, the last set value will be used.

Example:
.nf
 curl --expect100-timeout 2.5 -T file https://example.com
.fi

See also \fI--connect-timeout\fP. Added in 7.47.0.
.IP "\-\-fail-early"
Fail and exit on the first detected transfer error.

When curl is used to do multiple transfers on the command line, it will
attempt to operate on each given URL, one by one. By default, it will ignore
errors if there are more URLs given and the last URL's success will determine
the error code curl returns. So early failures will be "hidden" by subsequent
successful transfers.

Using this option, curl will instead return an error on the first transfer
that fails, independent of the amount of URLs that are given on the command
line. This way, no transfer failures go undetected by scripts and similar.

This option is global and does not need to be specified for each use of \fI\-:, \-\-next\fP.

This option does not imply \fI\-f, \-\-fail\fP, which causes transfers to fail due to the
server's HTTP status code. You can combine the two options, however note \-\-fail
is not global and is therefore contained by \fI\-:, \-\-next\fP.

Providing --fail-early multiple times has no extra effect.
Disable it again with --no-fail-early.

Example:
.nf
 curl --fail-early https://example.com https://two.example
.fi

See also \fI-f, --fail\fP and \fI--fail-with-body\fP. Added in 7.52.0.
.IP "\-\-fail-with-body"
(HTTP) Return an error on server errors where the HTTP response code is 400 or
greater). In normal cases when an HTTP server fails to deliver a document, it
returns an HTML document stating so (which often also describes why and
more). This flag will still allow curl to output and save that content but
also to return error 22.

This is an alternative option to \-\-fail which makes curl fail for the same
circumstances but without saving the content.

Providing --fail-with-body multiple times has no extra effect.
Disable it again with --no-fail-with-body.

Example:
.nf
 curl --fail-with-body https://example.com
.fi

See also \fI-f, --fail\fP. This option is mutually exclusive to \fI-f, --fail\fP. Added in 7.76.0.
.IP "\-f, \-\-fail"
(HTTP) Fail fast with no output at all on server errors. This is useful to enable
scripts and users to better deal with failed attempts. In normal cases when an
HTTP server fails to deliver a document, it returns an HTML document stating
so (which often also describes why and more). This flag will prevent curl from
outputting that and return error 22.

This method is not fail-safe and there are occasions where non-successful
response codes will slip through, especially when authentication is involved
(response codes 401 and 407).

Providing --fail multiple times has no extra effect.
Disable it again with --no-fail.

Example:
.nf
 curl --fail https://example.com
.fi

See also \fI--fail-with-body\fP. This option is mutually exclusive to \fI--fail-with-body\fP.
.IP "\-\-false-start"
(TLS) Tells curl to use false start during the TLS handshake. False start is a mode
where a TLS client will start sending application data before verifying the
server's Finished message, thus saving a round trip when performing a full
handshake.

This is currently only implemented in the NSS and Secure Transport (on iOS 7.0
or later, or OS X 10.9 or later) backends.

Providing --false-start multiple times has no extra effect.
Disable it again with --no-false-start.

Example:
.nf
 curl --false-start https://example.com
.fi

See also \fI--tcp-fastopen\fP. Added in 7.42.0.
.IP "\-\-form-escape"
(HTTP) Tells curl to pass on names of multipart form fields and files using
backslash-escaping instead of percent-encoding.

If --form-escape is provided several times, the last set value will be used.

Example:
.nf
 curl --form-escape -F 'field\\name=curl' -F 'file=@load"this' https://example.com
.fi

See also \fI-F, --form\fP. Added in 7.81.0.
.IP "\-\-form-string <name=string>"
(HTTP SMTP IMAP) Similar to \-\-form except that the value string for the named parameter is used
literally. Leading '@' and '<' characters, and the ';type=' string in
the value have no special meaning. Use this in preference to \-\-form if
there's any possibility that the string value may accidentally trigger the
\(aq@' or '<' features of \fI\-F, \-\-form\fP.

--form-string can be used several times in a command line

Example:
.nf
 curl --form-string "data" https://example.com
.fi

See also \fI-F, --form\fP.
.IP "\-F, \-\-form <name=content>"
(HTTP SMTP IMAP) For HTTP protocol family, this lets curl emulate a filled-in form in which a
user has pressed the submit button. This causes curl to POST data using the
Content-Type multipart/form-data according to RFC 2388.

For SMTP and IMAP protocols, this is the means to compose a multipart mail
message to transmit.

This enables uploading of binary files etc. To force the 'content' part to be
a file, prefix the file name with an @ sign. To just get the content part from
a file, prefix the file name with the symbol <. The difference between @ and <
is then that @ makes a file get attached in the post as a file upload, while
the < makes a text field and just get the contents for that text field from a
file.

Tell curl to read content from stdin instead of a file by using \- as
filename. This goes for both @ and < constructs. When stdin is used, the
contents is buffered in memory first by curl to determine its size and allow a
possible resend. Defining a part's data from a named non-regular file (such
as a named pipe or similar) is unfortunately not subject to buffering and will
be effectively read at transmission time; since the full size is unknown
before the transfer starts, such data is sent as chunks by HTTP and rejected
by IMAP.

Example: send an image to an HTTP server, where 'profile' is the name of the
form-field to which the file portrait.jpg will be the input:

.nf
 curl \-F profile=@portrait.jpg https://example.com/upload.cgi
.fi

Example: send your name and shoe size in two text fields to the server:

.nf
 curl \-F name=John \-F shoesize=11 https://example.com/
.fi

Example: send your essay in a text field to the server. Send it as a plain
text field, but get the contents for it from a local file:

.nf
 curl \-F "story=<hugefile.txt" https://example.com/
.fi

You can also tell curl what Content-Type to use by using 'type=', in a manner
similar to:

.nf
 curl \-F "web=@index.html;type=text/html" example.com
.fi

or

.nf
 curl \-F "name=daniel;type=text/foo" example.com
.fi

You can also explicitly change the name field of a file upload part by setting
filename=, like this:

.nf
 curl \-F "file=@localfile;filename=nameinpost" example.com
.fi

If filename/path contains ',' or ';', it must be quoted by double-quotes like:

.nf
 curl \-F "file=@\\"local,file\\";filename=\\"name;in;post\\"" example.com
.fi

or

.nf
 curl \-F 'file=@"local,file";filename="name;in;post"' example.com
.fi

Note that if a filename/path is quoted by double-quotes, any double-quote
or backslash within the filename must be escaped by backslash.

Quoting must also be applied to non-file data if it contains semicolons,
leading/trailing spaces or leading double quotes:

.nf
 curl \-F 'colors="red; green; blue";type=text/x-myapp' example.com
.fi

You can add custom headers to the field by setting headers=, like

.nf
  curl \-F "submit=OK;headers=\\"X-submit-type: OK\\"" example.com
.fi

or

.nf
  curl \-F "submit=OK;headers=@headerfile" example.com
.fi

The headers= keyword may appear more that once and above notes about quoting
apply. When headers are read from a file, Empty lines and lines starting
with '#' are comments and ignored; each header can be folded by splitting
between two words and starting the continuation line with a space; embedded
carriage-returns and trailing spaces are stripped.
Here is an example of a header file contents:

.nf
  # This file contain two headers.
  X-header-1: this is a header
.fi

.nf
  # The following header is folded.
  X-header-2: this is
   another header
.fi

To support sending multipart mail messages, the syntax is extended as follows:
.br
\- name can be omitted: the equal sign is the first character of the argument,
.br
\- if data starts with '(', this signals to start a new multipart: it can be
followed by a content type specification.
.br
\- a multipart can be terminated with a '=)' argument.

Example: the following command sends an SMTP mime email consisting in an
inline part in two alternative formats: plain text and HTML. It attaches a
text file:

.nf
 curl \-F '=(;type=multipart/alternative' \\
      \-F '=plain text message' \\
      \-F '= <body>HTML message</body>;type=text/html' \\
      \-F '=)' \-F '=@textfile.txt' ...  smtp://example.com
.fi

Data can be encoded for transfer using encoder=. Available encodings are
\fIbinary\fP and \fI8bit\fP that do nothing else than adding the corresponding
Content-Transfer-Encoding header, \fI7bit\fP that only rejects 8-bit characters
with a transfer error, \fIquoted-printable\fP and \fIbase64\fP that encodes data
according to the corresponding schemes, limiting lines length to 76
characters.

Example: send multipart mail with a quoted-printable text message and a
base64 attached file:

.nf
 curl \-F '=text message;encoder=quoted-printable' \\
      \-F '=@localfile;encoder=base64' ... smtp://example.com
.fi

See further examples and details in the MANUAL.

--form can be used several times in a command line

Example:
.nf
 curl --form "name=curl" --form "file=@loadthis" https://example.com
.fi

See also \fI-d, --data\fP, \fI--form-string\fP and \fI--form-escape\fP. This option is mutually exclusive to \fI-d, --data\fP and \fI-I, --head\fP and \fI-T, --upload-file\fP.
.IP "\-\-ftp-account <data>"
(FTP) When an FTP server asks for "account data" after user name and password has
been provided, this data is sent off using the ACCT command.

If --ftp-account is provided several times, the last set value will be used.

Example:
.nf
 curl --ftp-account "mr.robot" ftp://example.com/
.fi

See also \fI-u, --user\fP.
.IP "\-\-ftp-alternative-to-user <command>"
(FTP) If authenticating with the USER and PASS commands fails, send this command.
When connecting to Tumbleweed's Secure Transport server over FTPS using a
client certificate, using "SITE AUTH" will tell the server to retrieve the
username from the certificate.

If --ftp-alternative-to-user is provided several times, the last set value will be used.

Example:
.nf
 curl --ftp-alternative-to-user "U53r" ftp://example.com
.fi

See also \fI--ftp-account\fP and \fI-u, --user\fP.
.IP "\-\-ftp-create-dirs"
(FTP SFTP) When an FTP or SFTP URL/operation uses a path that does not currently exist on
the server, the standard behavior of curl is to fail. Using this option, curl
will instead attempt to create missing directories.

Providing --ftp-create-dirs multiple times has no extra effect.
Disable it again with --no-ftp-create-dirs.

Example:
.nf
 curl --ftp-create-dirs -T file ftp://example.com/remote/path/file
.fi

See also \fI--create-dirs\fP.
.IP "\-\-ftp-method <method>"
(FTP) Control what method curl should use to reach a file on an FTP(S)
server. The method argument should be one of the following alternatives:
.RS
.IP multicwd
curl does a single CWD operation for each path part in the given URL. For deep
hierarchies this means many commands. This is how RFC 1738 says it should
be done. This is the default but the slowest behavior.
.IP nocwd
curl does no CWD at all. curl will do SIZE, RETR, STOR etc and give a full
path to the server for all these commands. This is the fastest behavior.
.IP singlecwd
curl does one CWD with the full target directory and then operates on the file
\(dqnormally" (like in the multicwd case). This is somewhat more standards
compliant than 'nocwd' but without the full penalty of 'multicwd'.
.RE

If --ftp-method is provided several times, the last set value will be used.

Examples:
.nf
 curl --ftp-method multicwd ftp://example.com/dir1/dir2/file
 curl --ftp-method nocwd ftp://example.com/dir1/dir2/file
 curl --ftp-method singlecwd ftp://example.com/dir1/dir2/file
.fi

See also \fI-l, --list-only\fP.
.IP "\-\-ftp-pasv"
(FTP) Use passive mode for the data connection. Passive is the internal default
behavior, but using this option can be used to override a previous \-\-ftp-port
option.

Reversing an enforced passive really is not doable but you must then instead
enforce the correct \-\-ftp-port again.

Passive mode means that curl will try the EPSV command first and then PASV,
unless \-\-disable-epsv is used.

Providing --ftp-pasv multiple times has no extra effect.
Disable it again with --no-ftp-pasv.

Example:
.nf
 curl --ftp-pasv ftp://example.com/
.fi

See also \fI--disable-epsv\fP.
.IP "\-P, \-\-ftp-port <address>"
(FTP) Reverses the default initiator/listener roles when connecting with FTP. This
option makes curl use active mode. curl then tells the server to connect back
to the client's specified address and port, while passive mode asks the server
to setup an IP address and port for it to connect to. <address> should be one
of:
.RS
.IP interface
e.g. "eth0" to specify which interface's IP address you want to use (Unix only)
.IP "IP address"
e.g. "************" to specify the exact IP address
.IP "host name"
e.g. "my.host.domain" to specify the machine
.IP "-"
make curl pick the same IP address that is already used for the control
connection
.RE

Disable the use of PORT with \fI\-\-ftp-pasv\fP. Disable the attempt to use the EPRT
command instead of PORT by using \fI\-\-disable-eprt\fP. EPRT is really PORT++.

You can also append ":[start]-[end]\&" to the right of the address, to tell
curl what TCP port range to use. That means you specify a port range, from a
lower to a higher number. A single number works as well, but do note that it
increases the risk of failure since the port may not be available.


If --ftp-port is provided several times, the last set value will be used.

Examples:
.nf
 curl -P - ftp:/example.com
 curl -P eth0 ftp:/example.com
 curl -P *********** ftp:/example.com
.fi

See also \fI--ftp-pasv\fP and \fI--disable-eprt\fP.
.IP "\-\-ftp-pret"
(FTP) Tell curl to send a PRET command before PASV (and EPSV). Certain FTP servers,
mainly drftpd, require this non-standard command for directory listings as
well as up and downloads in PASV mode.

Providing --ftp-pret multiple times has no extra effect.
Disable it again with --no-ftp-pret.

Example:
.nf
 curl --ftp-pret ftp://example.com/
.fi

See also \fI-P, --ftp-port\fP and \fI--ftp-pasv\fP.
.IP "\-\-ftp-skip-pasv-ip"
(FTP) Tell curl to not use the IP address the server suggests in its response
to curl's PASV command when curl connects the data connection. Instead curl
will re-use the same IP address it already uses for the control
connection.

Since curl 7.74.0 this option is enabled by default.

This option has no effect if PORT, EPRT or EPSV is used instead of PASV.

Providing --ftp-skip-pasv-ip multiple times has no extra effect.
Disable it again with --no-ftp-skip-pasv-ip.

Example:
.nf
 curl --ftp-skip-pasv-ip ftp://example.com/
.fi

See also \fI--ftp-pasv\fP.
.IP "\-\-ftp-ssl-ccc-mode <active/passive>"
(FTP) Sets the CCC mode. The passive mode will not initiate the shutdown, but
instead wait for the server to do it, and will not reply to the shutdown from
the server. The active mode initiates the shutdown and waits for a reply from
the server.

Providing --ftp-ssl-ccc-mode multiple times has no extra effect.
Disable it again with --no-ftp-ssl-ccc-mode.

Example:
.nf
 curl --ftp-ssl-ccc-mode active --ftp-ssl-ccc ftps://example.com/
.fi

See also \fI--ftp-ssl-ccc\fP.
.IP "\-\-ftp-ssl-ccc"
(FTP) Use CCC (Clear Command Channel) Shuts down the SSL/TLS layer after
authenticating. The rest of the control channel communication will be
unencrypted. This allows NAT routers to follow the FTP transaction. The
default mode is passive.

Providing --ftp-ssl-ccc multiple times has no extra effect.
Disable it again with --no-ftp-ssl-ccc.

Example:
.nf
 curl --ftp-ssl-ccc ftps://example.com/
.fi

See also \fI--ssl\fP and \fI--ftp-ssl-ccc-mode\fP.
.IP "\-\-ftp-ssl-control"
(FTP) Require SSL/TLS for the FTP login, clear for transfer.  Allows secure
authentication, but non-encrypted data transfers for efficiency.  Fails the
transfer if the server does not support SSL/TLS.

Providing --ftp-ssl-control multiple times has no extra effect.
Disable it again with --no-ftp-ssl-control.

Example:
.nf
 curl --ftp-ssl-control ftp://example.com
.fi

See also \fI--ssl\fP.
.IP "\-G, \-\-get"
When used, this option will make all data specified with \fI\-d, \-\-data\fP, \-\-data-binary
or \-\-data-urlencode to be used in an HTTP GET request instead of the POST
request that otherwise would be used. The data will be appended to the URL
with a '?' separator.

If used in combination with \fI\-I, \-\-head\fP, the POST data will instead be appended to
the URL with a HEAD request.

Providing --get multiple times has no extra effect.
Disable it again with --no-get.

Examples:
.nf
 curl --get https://example.com
 curl --get -d "tool=curl" -d "age=old" https://example.com
 curl --get -I -d "tool=curl" https://example.com
.fi

See also \fI-d, --data\fP and \fI-X, --request\fP.
.IP "\-g, \-\-globoff"
This option switches off the "URL globbing parser". When you set this option,
you can specify URLs that contain the letters {}[] without having curl itself
interpret them. Note that these letters are not normal legal URL contents but
they should be encoded according to the URI standard.

Providing --globoff multiple times has no extra effect.
Disable it again with --no-globoff.

Example:
.nf
 curl -g "https://example.com/{[]}}}}"
.fi

See also \fI-K, --config\fP and \fI-q, --disable\fP.
.IP "\-\-happy-eyeballs-timeout-ms <milliseconds>"
Happy Eyeballs is an algorithm that attempts to connect to both IPv4 and IPv6
addresses for dual-stack hosts, giving IPv6 a head-start of the specified
number of milliseconds. If the IPv6 address cannot be connected to within that
time, then a connection attempt is made to the IPv4 address in parallel. The
first connection to be established is the one that is used.

The range of suggested useful values is limited. Happy Eyeballs RFC 6555 says
\(dqIt is RECOMMENDED that connection attempts be paced 150-250 ms apart to
balance human factors against network load." libcurl currently defaults to
200 ms. Firefox and Chrome currently default to 300 ms.

If --happy-eyeballs-timeout-ms is provided several times, the last set value will be used.

Example:
.nf
 curl --happy-eyeballs-timeout-ms 500 https://example.com
.fi

See also \fI-m, --max-time\fP and \fI--connect-timeout\fP. Added in 7.59.0.
.IP "\-\-haproxy-protocol"
(HTTP) Send a HAProxy PROXY protocol v1 header at the beginning of the
connection. This is used by some load balancers and reverse proxies to
indicate the client's true IP address and port.

This option is primarily useful when sending test requests to a service that
expects this header.

Providing --haproxy-protocol multiple times has no extra effect.
Disable it again with --no-haproxy-protocol.

Example:
.nf
 curl --haproxy-protocol https://example.com
.fi

See also \fI-x, --proxy\fP. Added in 7.60.0.
.IP "\-I, \-\-head"
(HTTP FTP FILE) Fetch the headers only! HTTP-servers feature the command HEAD which this uses
to get nothing but the header of a document. When used on an FTP or FILE file,
curl displays the file size and last modification time only.

Providing --head multiple times has no extra effect.
Disable it again with --no-head.

Example:
.nf
 curl -I https://example.com
.fi

See also \fI-G, --get\fP, \fI-v, --verbose\fP and \fI--trace-ascii\fP.
.IP "\-H, \-\-header <header/@file>"
(HTTP IMAP SMTP) Extra header to include in information sent. When used within an HTTP request,
it is added to the regular request headers.

For an IMAP or SMTP MIME uploaded mail built with \-\-form options, it is
prepended to the resulting MIME document, effectively including it at the mail
global level. It does not affect raw uploaded mails (Added in 7.56.0).

You may specify any number of extra headers. Note that if you should add a
custom header that has the same name as one of the internal ones curl would
use, your externally set header will be used instead of the internal one.
This allows you to make even trickier stuff than curl would normally do. You
should not replace internally set headers without knowing perfectly well what
you are doing. Remove an internal header by giving a replacement without
content on the right side of the colon, as in: \-H "Host:". If you send the
custom header with no-value then its header must be terminated with a
semicolon, such as \-H "X-Custom-Header;" to send "X-Custom-Header:".

curl will make sure that each header you add/replace is sent with the proper
end-of-line marker, you should thus \fBnot\fP add that as a part of the header
content: do not add newlines or carriage returns, they will only mess things
up for you.

This option can take an argument in @filename style, which then adds a header
for each line in the input file. Using @- will make curl read the header file
from stdin. Added in 7.55.0.

Please note that most anti-spam utilities check the presence and value of
several MIME mail headers: these are "From:", "To:", "Date:" and "Subject:"
among others and should be added with this option.

You need \-\-proxy-header to send custom headers intended for an HTTP
proxy. Added in 7.37.0.

Passing on a "Transfer-Encoding: chunked" header when doing an HTTP request
with a request body, will make curl send the data using chunked encoding.

\fBWARNING\fP: headers set with this option will be set in all HTTP requests
\- even after redirects are followed, like when told with \fI\-L, \-\-location\fP. This can
lead to the header being sent to other hosts than the original host, so
sensitive headers should be used with caution combined with following
redirects.

--header can be used several times in a command line

Examples:
.nf
 curl -H "X-First-Name: Joe" https://example.com
 curl -H "User-Agent: yes-please/2000" https://example.com
 curl -H "Host:" https://example.com
.fi

See also \fI-A, --user-agent\fP and \fI-e, --referer\fP.
.IP "\-h, \-\-help <category>"
Usage help. This lists all commands of the <category>.
If no arg was provided, curl will display the most important
command line arguments.
If the argument "all" was provided, curl will display all options available.
If the argument "category" was provided, curl will display all categories and
their meanings.

Providing --help multiple times has no extra effect.
Disable it again with --no-help.

Example:
.nf
 curl --help all
.fi

See also \fI-v, --verbose\fP.
.IP "\-\-hostpubmd5 <md5>"
(SFTP SCP) Pass a string containing 32 hexadecimal digits. The string should
be the 128 bit MD5 checksum of the remote host's public key, curl will refuse
the connection with the host unless the md5sums match.

If --hostpubmd5 is provided several times, the last set value will be used.

Example:
.nf
 curl --hostpubmd5 e5c1c49020640a5ab0f2034854c321a8 sftp://example.com/
.fi

See also \fI--hostpubsha256\fP.
.IP "\-\-hostpubsha256 <sha256>"
(SFTP SCP) Pass a string containing a Base64-encoded SHA256 hash of the remote
host's public key. Curl will refuse the connection with the host
unless the hashes match.

If --hostpubsha256 is provided several times, the last set value will be used.

Example:
.nf
 curl --hostpubsha256 NDVkMTQxMGQ1ODdmMjQ3MjczYjAyOTY5MmRkMjVmNDQ= sftp://example.com/
.fi

See also \fI--hostpubmd5\fP. Added in 7.80.0.
.IP "\-\-hsts <file name>"
(HTTPS) This option enables HSTS for the transfer. If the file name points to an
existing HSTS cache file, that will be used. After a completed transfer, the
cache will be saved to the file name again if it has been modified.

Specify a "" file name (zero length) to avoid loading/saving and make curl
just handle HSTS in memory.

If this option is used several times, curl will load contents from all the
files but the last one will be used for saving.

--hsts can be used several times in a command line

Example:
.nf
 curl --hsts cache.txt https://example.com
.fi

See also \fI--proto\fP. Added in 7.74.0.
.IP "\-\-http0.9"
(HTTP) Tells curl to be fine with HTTP version 0.9 response.

HTTP/0.9 is a completely headerless response and therefore you can also
connect with this to non-HTTP servers and still get a response since curl will
simply transparently downgrade \- if allowed.

Since curl 7.66.0, HTTP/0.9 is disabled by default.

Providing --http0.9 multiple times has no extra effect.
Disable it again with --no-http0.9.

Example:
.nf
 curl --http0.9 https://example.com
.fi

See also \fI--http1.1\fP, \fI--http2\fP and \fI--http3\fP. Added in 7.64.0.
.IP "\-0, \-\-http1.0"
(HTTP) Tells curl to use HTTP version 1.0 instead of using its internally preferred
HTTP version.

Providing --http1.0 multiple times has no extra effect.

Example:
.nf
 curl --http1.0 https://example.com
.fi

See also \fI--http0.9\fP and \fI--http1.1\fP. This option is mutually exclusive to \fI--http1.1\fP and \fI--http2\fP and \fI--http2-prior-knowledge\fP and \fI--http3\fP.
.IP "\-\-http1.1"
(HTTP) Tells curl to use HTTP version 1.1.

Providing --http1.1 multiple times has no extra effect.

Example:
.nf
 curl --http1.1 https://example.com
.fi

See also \fI-0, --http1.0\fP and \fI--http0.9\fP. This option is mutually exclusive to \fI-0, --http1.0\fP and \fI--http2\fP and \fI--http2-prior-knowledge\fP and \fI--http3\fP. Added in 7.33.0.
.IP "\-\-http2-prior-knowledge"
(HTTP) Tells curl to issue its non-TLS HTTP requests using HTTP/2 without HTTP/1.1
Upgrade. It requires prior knowledge that the server supports HTTP/2 straight
away. HTTPS requests will still do HTTP/2 the standard way with negotiated
protocol version in the TLS handshake.

Providing --http2-prior-knowledge multiple times has no extra effect.
Disable it again with --no-http2-prior-knowledge.

Example:
.nf
 curl --http2-prior-knowledge https://example.com
.fi

See also \fI--http2\fP and \fI--http3\fP. \fI--http2-prior-knowledge\fP requires that the underlying libcurl was built to support HTTP/2. This option is mutually exclusive to \fI--http1.1\fP and \fI-0, --http1.0\fP and \fI--http2\fP and \fI--http3\fP. Added in 7.49.0.
.IP "\-\-http2"
(HTTP) Tells curl to use HTTP version 2.

For HTTPS, this means curl will attempt to negotiate HTTP/2 in the TLS
handshake. curl does this by default.

For HTTP, this means curl will attempt to upgrade the request to HTTP/2 using
the Upgrade: request header.

When curl uses HTTP/2 over HTTPS, it does not itself insist on TLS 1.2 or
higher even though that is required by the specification. A user can add this
version requirement with \fI\-\-tlsv1.2\fP.

Providing --http2 multiple times has no extra effect.

Example:
.nf
 curl --http2 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http3\fP. \fI--http2\fP requires that the underlying libcurl was built to support HTTP/2. This option is mutually exclusive to \fI--http1.1\fP and \fI-0, --http1.0\fP and \fI--http2-prior-knowledge\fP and \fI--http3\fP. Added in 7.33.0.
.IP "\-\-http3"
(HTTP) **WARNING**: this option is experimental. Do not use in production.

Tells curl to use HTTP version 3 directly to the host and port number used in
the URL. A normal HTTP/3 transaction will be done to a host and then get
redirected via Alt-Svc, but this option allows a user to circumvent that when
you know that the target speaks HTTP/3 on the given host and port.

This option will make curl fail if a QUIC connection cannot be established, it
cannot fall back to a lower HTTP version on its own.

Providing --http3 multiple times has no extra effect.

Example:
.nf
 curl --http3 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http2\fP. \fI--http3\fP requires that the underlying libcurl was built to support HTTP/3. This option is mutually exclusive to \fI--http1.1\fP and \fI-0, --http1.0\fP and \fI--http2\fP and \fI--http2-prior-knowledge\fP. Added in 7.66.0.
.IP "\-\-ignore-content-length"
(FTP HTTP) For HTTP, Ignore the Content-Length header. This is particularly useful for
servers running Apache 1.x, which will report incorrect Content-Length for
files larger than 2 gigabytes.

For FTP (since 7.46.0), skip the RETR command to figure out the size before
downloading a file.

This option does not work for HTTP if libcurl was built to use hyper.

Providing --ignore-content-length multiple times has no extra effect.
Disable it again with --no-ignore-content-length.

Example:
.nf
 curl --ignore-content-length https://example.com
.fi

See also \fI--ftp-skip-pasv-ip\fP.
.IP "\-i, \-\-include"
Include the HTTP response headers in the output. The HTTP response headers can
include things like server name, cookies, date of the document, HTTP version
and more...

To view the request headers, consider the \-\-verbose option.

Providing --include multiple times has no extra effect.
Disable it again with --no-include.

Example:
.nf
 curl -i https://example.com
.fi

See also \fI-v, --verbose\fP.
.IP "\-k, \-\-insecure"
(TLS SFTP SCP) By default, every secure connection curl makes is verified to be secure before
the transfer takes place. This option makes curl skip the verification step
and proceed without checking.

When this option is not used for protocols using TLS, curl verifies the
server's TLS certificate before it continues: that the certificate contains
the right name which matches the host name used in the URL and that the
certificate has been signed by a CA certificate present in the cert store.
See this online resource for further details:
.nf
 https://curl.se/docs/sslcerts.html
.fi

For SFTP and SCP, this option makes curl skip the \fIknown_hosts\fP verification.
\fIknown_hosts\fP is a file normally stored in the user's home directory in the
\(dq.ssh" subdirectory, which contains host names and their public keys.

\fBWARNING\fP: using this option makes the transfer insecure.

Providing --insecure multiple times has no extra effect.
Disable it again with --no-insecure.

Example:
.nf
 curl --insecure https://example.com
.fi

See also \fI--proxy-insecure\fP, \fI--cacert\fP and \fI--capath\fP.
.IP "\-\-interface <name>"
Perform an operation using a specified interface. You can enter interface
name, IP address or host name. An example could look like:

.nf
 curl \-\-interface eth0:1 https://www.example.com/
.fi

On Linux it can be used to specify a VRF, but the binary needs to either
have CAP_NET_RAW or to be run as root. More information about Linux VRF:
https://www.kernel.org/doc/Documentation/networking/vrf.txt

If --interface is provided several times, the last set value will be used.

Example:
.nf
 curl --interface eth0 https://example.com
.fi

See also \fI--dns-interface\fP.
.IP "\-4, \-\-ipv4"
This option tells curl to use IPv4 addresses only, and not for example try
IPv6.

Providing --ipv4 multiple times has no extra effect.
Disable it again with --no-ipv4.

Example:
.nf
 curl --ipv4 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http2\fP. This option is mutually exclusive to \fI-6, --ipv6\fP.
.IP "\-6, \-\-ipv6"
This option tells curl to use IPv6 addresses only, and not for example try
IPv4.

Providing --ipv6 multiple times has no extra effect.
Disable it again with --no-ipv6.

Example:
.nf
 curl --ipv6 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http2\fP. This option is mutually exclusive to \fI-4, --ipv4\fP.
.IP "\-\-json <data>"
(HTTP) Sends the specified JSON data in a POST request to the HTTP server. \-\-json
works as a shortcut for passing on these three options:

.nf
 \-\-data [arg]
 \-\-header "Content-Type: application/json"
 \-\-header "Accept: application/json"
.fi

There is \fI\fPno verification\fI\fP that the passed in data is actual JSON or that
the syntax is correct.

If you start the data with the letter @, the rest should be a file name to
read the data from, or a single dash (-) if you want curl to read the data
from stdin. Posting data from a file named 'foobar' would thus be done with
\fI\-\-json\fP @foobar and to instead read the data from stdin, use \-\-json @-.

If this option is used more than once on the same command line, the additional
data pieces will be concatenated to the previous before sending.

The headers this option sets can be overridden with \-\-header as usual.

--json can be used several times in a command line

Examples:
.nf
 curl --json '{ "drink": "coffe" }' https://example.com
 curl --json '{ "drink":' --json ' "coffe" }' https://example.com
 curl --json @prepared https://example.com
 curl --json @- https://example.com < json.txt
.fi

See also \fI--data-binary\fP and \fI--data-raw\fP. This option is mutually exclusive to \fI-F, --form\fP and \fI-I, --head\fP and \fI-T, --upload-file\fP. Added in 7.82.0.
.IP "\-j, \-\-junk-session-cookies"
(HTTP) When curl is told to read cookies from a given file, this option will make it
discard all "session cookies". This will basically have the same effect as if
a new session is started. Typical browsers always discard session cookies when
they are closed down.

Providing --junk-session-cookies multiple times has no extra effect.
Disable it again with --no-junk-session-cookies.

Example:
.nf
 curl --junk-session-cookies -b cookies.txt https://example.com
.fi

See also \fI-b, --cookie\fP and \fI-c, --cookie-jar\fP.
.IP "\-\-keepalive-time <seconds>"
This option sets the time a connection needs to remain idle before sending
keepalive probes and the time between individual keepalive probes. It is
currently effective on operating systems offering the TCP_KEEPIDLE and
TCP_KEEPINTVL socket options (meaning Linux, recent AIX, HP-UX and more).
Keepalives are used by the TCP stack to detect broken networks on idle
connections. The number of missed keepalive probes before declaring the
connection down is OS dependent and is commonly 9 or 10. This option has no
effect if \-\-no-keepalive is used.

If unspecified, the option defaults to 60 seconds.

If --keepalive-time is provided several times, the last set value will be used.

Example:
.nf
 curl --keepalive-time 20 https://example.com
.fi

See also \fI--no-keepalive\fP and \fI-m, --max-time\fP.
.IP "\-\-key-type <type>"
(TLS) Private key file type. Specify which type your \-\-key provided private key
is. DER, PEM, and ENG are supported. If not specified, PEM is assumed.

If --key-type is provided several times, the last set value will be used.

Example:
.nf
 curl --key-type DER --key here https://example.com
.fi

See also \fI--key\fP.
.IP "\-\-key <key>"
(TLS SSH) Private key file name. Allows you to provide your private key in this separate
file. For SSH, if not specified, curl tries the following candidates in order:
\(aq~/.ssh/id_rsa', '~/.ssh/id_dsa', './id_rsa', './id_dsa'.

If curl is built against OpenSSL library, and the engine pkcs11 is available,
then a PKCS#11 URI (RFC 7512) can be used to specify a private key located in a
PKCS#11 device. A string beginning with "pkcs11:" will be interpreted as a
PKCS#11 URI. If a PKCS#11 URI is provided, then the \-\-engine option will be set
as "pkcs11" if none was provided and the \-\-key-type option will be set as
\(dqENG" if none was provided.

If curl is built against Secure Transport or Schannel then this option is
ignored for TLS protocols (HTTPS, etc). Those backends expect the private key
to be already present in the keychain or PKCS#12 file containing the
certificate.

If --key is provided several times, the last set value will be used.

Example:
.nf
 curl --cert certificate --key here https://example.com
.fi

See also \fI--key-type\fP and \fI-E, --cert\fP.
.IP "\-\-krb <level>"
(FTP) Enable Kerberos authentication and use. The level must be entered and should
be one of 'clear', 'safe', 'confidential', or 'private'. Should you use a
level that is not one of these, 'private' will instead be used.

If --krb is provided several times, the last set value will be used.

Example:
.nf
 curl --krb clear ftp://example.com/
.fi

See also \fI--delegation\fP and \fI--ssl\fP. \fI--krb\fP requires that the underlying libcurl was built to support Kerberos.
.IP "\-\-libcurl <file>"
Append this option to any ordinary curl command line, and you will get
libcurl-using C source code written to the file that does the equivalent
of what your command-line operation does!

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

If --libcurl is provided several times, the last set value will be used.

Example:
.nf
 curl --libcurl client.c https://example.com
.fi

See also \fI-v, --verbose\fP.
.IP "\-\-limit-rate <speed>"
Specify the maximum transfer rate you want curl to use \- for both downloads
and uploads. This feature is useful if you have a limited pipe and you would like
your transfer not to use your entire bandwidth. To make it slower than it
otherwise would be.

The given speed is measured in bytes/second, unless a suffix is appended.
Appending 'k' or 'K' will count the number as kilobytes, 'm' or 'M' makes it
megabytes, while 'g' or 'G' makes it gigabytes. The suffixes (k, M, G, T, P)
are 1024 based. For example 1k is 1024. Examples: 200K, 3m and 1G.

The rate limiting logic works on averaging the transfer speed to no more than
the set threshold over a period of multiple seconds.

If you also use the \-\-speed-limit option, that option will take precedence and
might cripple the rate-limiting slightly, to help keeping the speed-limit
logic working.

If --limit-rate is provided several times, the last set value will be used.

Examples:
.nf
 curl --limit-rate 100K https://example.com
 curl --limit-rate 1000 https://example.com
 curl --limit-rate 10M https://example.com
.fi

See also \fI-Y, --speed-limit\fP and \fI-y, --speed-time\fP.
.IP "\-l, \-\-list-only"
(FTP POP3) (FTP)
When listing an FTP directory, this switch forces a name-only view. This is
especially useful if the user wants to machine-parse the contents of an FTP
directory since the normal directory view does not use a standard look or
format. When used like this, the option causes an NLST command to be sent to
the server instead of LIST.

Note: Some FTP servers list only files in their response to NLST; they do not
include sub-directories and symbolic links.

(POP3)
When retrieving a specific email from POP3, this switch forces a LIST command
to be performed instead of RETR. This is particularly useful if the user wants
to see if a specific message-id exists on the server and what size it is.

Note: When combined with \fI\-X, \-\-request\fP, this option can be used to send a UIDL
command instead, so the user may use the email's unique identifier rather than
its message-id to make the request.

Providing --list-only multiple times has no extra effect.
Disable it again with --no-list-only.

Example:
.nf
 curl --list-only ftp://example.com/dir/
.fi

See also \fI-Q, --quote\fP and \fI-X, --request\fP.
.IP "\-\-local-port <num/range>"
Set a preferred single number or range (FROM-TO) of local port numbers to use
for the connection(s).  Note that port numbers by nature are a scarce resource
that will be busy at times so setting this range to something too narrow might
cause unnecessary connection setup failures.

If --local-port is provided several times, the last set value will be used.

Example:
.nf
 curl --local-port 1000-3000 https://example.com
.fi

See also \fI-g, --globoff\fP.
.IP "\-\-location-trusted"
(HTTP) Like \fI\-L, \-\-location\fP, but will allow sending the name + password to all hosts that
the site may redirect to. This may or may not introduce a security breach if
the site redirects you to a site to which you will send your authentication
info (which is plaintext in the case of HTTP Basic authentication).

Providing --location-trusted multiple times has no extra effect.
Disable it again with --no-location-trusted.

Example:
.nf
 curl --location-trusted -u user:password https://example.com
.fi

See also \fI-u, --user\fP.
.IP "\-L, \-\-location"
(HTTP) If the server reports that the requested page has moved to a different
location (indicated with a Location: header and a 3XX response code), this
option will make curl redo the request on the new place. If used together with
\-\-include or \fI\-I, \-\-head\fP, headers from all requested pages will be shown. When
authentication is used, curl only sends its credentials to the initial
host. If a redirect takes curl to a different host, it will not be able to
intercept the user+password. See also \-\-location-trusted on how to change
this. You can limit the amount of redirects to follow by using the
\-\-max-redirs option.

When curl follows a redirect and if the request is a POST, it will send the
following request with a GET if the HTTP response was 301, 302, or 303. If the
response code was any other 3xx code, curl will re-send the following request
using the same unmodified method.

You can tell curl to not change POST requests to GET after a 30x response by
using the dedicated options for that: \fI\-\-post301\fP, \-\-post302 and \fI\-\-post303\fP.

The method set with \-\-request overrides the method curl would otherwise select
to use.

Providing --location multiple times has no extra effect.
Disable it again with --no-location.

Example:
.nf
 curl -L https://example.com
.fi

See also \fI--resolve\fP and \fI--alt-svc\fP.
.IP "\-\-login-options <options>"
(IMAP LDAP POP3 SMTP) Specify the login options to use during server authentication.

You can use login options to specify protocol specific options that may be
used during authentication. At present only IMAP, POP3 and SMTP support
login options. For more information about login options please see RFC
2384, RFC 5092 and IETF draft draft-earhart-url-smtp-00.txt

If --login-options is provided several times, the last set value will be used.

Example:
.nf
 curl --login-options 'AUTH=*' imap://example.com
.fi

See also \fI-u, --user\fP. Added in 7.34.0.
.IP "\-\-mail-auth <address>"
(SMTP) Specify a single address. This will be used to specify the authentication
address (identity) of a submitted message that is being relayed to another
server.

If --mail-auth is provided several times, the last set value will be used.

Example:
.nf
 curl --mail-auth <EMAIL> -T mail smtp://example.com/
.fi

See also \fI--mail-rcpt\fP and \fI--mail-from\fP.
.IP "\-\-mail-from <address>"
(SMTP) Specify a single address that the given mail should get sent from.

If --mail-from is provided several times, the last set value will be used.

Example:
.nf
 curl --mail-from <EMAIL> -T mail smtp://example.com/
.fi

See also \fI--mail-rcpt\fP and \fI--mail-auth\fP.
.IP "\-\-mail-rcpt-allowfails"
(SMTP) When sending data to multiple recipients, by default curl will abort SMTP
conversation if at least one of the recipients causes RCPT TO command to
return an error.

The default behavior can be changed by passing \-\-mail-rcpt-allowfails
command-line option which will make curl ignore errors and proceed with the
remaining valid recipients.

If all recipients trigger RCPT TO failures and this flag is specified, curl
will still abort the SMTP conversation and return the error received from to
the last RCPT TO command.

Providing --mail-rcpt-allowfails multiple times has no extra effect.
Disable it again with --no-mail-rcpt-allowfails.

Example:
.nf
 curl --mail-rcpt-allowfails --mail-rcpt <EMAIL> smtp://example.com
.fi

See also \fI--mail-rcpt\fP. Added in 7.69.0.
.IP "\-\-mail-rcpt <address>"
(SMTP) Specify a single email address, user name or mailing list name. Repeat this
option several times to send to multiple recipients.

When performing an address verification (VRFY command), the recipient should be
specified as the user name or user name and domain (as per Section 3.5 of
RFC5321). (Added in 7.34.0)

When performing a mailing list expand (EXPN command), the recipient should be
specified using the mailing list name, such as "Friends" or "London-Office".
(Added in 7.34.0)

--mail-rcpt can be used several times in a command line

Example:
.nf
 curl --mail-rcpt <EMAIL> smtp://example.com
.fi

See also \fI--mail-rcpt-allowfails\fP.
.IP "\-M, \-\-manual"
Manual. Display the huge help text.

Providing --manual multiple times has no extra effect.
Disable it again with --no-manual.

Example:
.nf
 curl --manual
.fi

See also \fI-v, --verbose\fP, \fI--libcurl\fP and \fI--trace\fP.
.IP "\-\-max-filesize <bytes>"
(FTP HTTP MQTT) Specify the maximum size (in bytes) of a file to download. If the file
requested is larger than this value, the transfer will not start and curl will
return with exit code 63.

A size modifier may be used. For example, Appending 'k' or 'K' will count the
number as kilobytes, 'm' or 'M' makes it megabytes, while 'g' or 'G' makes it
gigabytes. Examples: 200K, 3m and 1G. (Added in 7.58.0)

\fBNOTE\fP: The file size is not always known prior to download, and for such
files this option has no effect even if the file transfer ends up being larger
than this given limit.
If --max-filesize is provided several times, the last set value will be used.

Example:
.nf
 curl --max-filesize 100K https://example.com
.fi

See also \fI--limit-rate\fP.
.IP "\-\-max-redirs <num>"
(HTTP) Set maximum number of redirections to follow. When \-\-location is used, to
prevent curl from following too many redirects, by default, the limit is
set to 50 redirects. Set this option to \-1 to make it unlimited.

If --max-redirs is provided several times, the last set value will be used.

Example:
.nf
 curl --max-redirs 3 --location https://example.com
.fi

See also \fI-L, --location\fP.
.IP "\-m, \-\-max-time <fractional seconds>"
Maximum time in seconds that you allow each transfer to take.  This is
useful for preventing your batch jobs from hanging for hours due to slow
networks or links going down.  Since 7.32.0, this option accepts decimal
values, but the actual timeout will decrease in accuracy as the specified
timeout increases in decimal precision.

If you enable retrying the transfer (\fI\-\-retry\fP) then the maximum time counter is
reset each time the transfer is retried. You can use \-\-retry-max-time to limit
the retry time.

If --max-time is provided several times, the last set value will be used.

Examples:
.nf
 curl --max-time 10 https://example.com
 curl --max-time 2.92 https://example.com
.fi

See also \fI--connect-timeout\fP and \fI--retry-max-time\fP.
.IP "\-\-metalink"
This option was previously used to specify a metalink resource. Metalink
support has been disabled in curl since 7.78.0 for security reasons.

If --metalink is provided several times, the last set value will be used.

Example:
.nf
 curl --metalink file https://example.com
.fi

See also \fI-Z, --parallel\fP.
.IP "\-\-negotiate"
(HTTP) Enables Negotiate (SPNEGO) authentication.

This option requires a library built with GSS-API or SSPI support. Use
\-\-version to see if your curl supports GSS-API/SSPI or SPNEGO.

When using this option, you must also provide a fake \-\-user option to activate
the authentication code properly. Sending a '-u :' is enough as the user name
and password from the \-\-user option are not actually used.

If this option is used several times, only the first one is used.

Providing --negotiate multiple times has no extra effect.

Example:
.nf
 curl --negotiate -u : https://example.com
.fi

See also \fI--basic\fP, \fI--ntlm\fP, \fI--anyauth\fP and \fI--proxy-negotiate\fP.
.IP "\-\-netrc-file <filename>"
This option is similar to \fI\-n, \-\-netrc\fP, except that you provide the path (absolute
or relative) to the netrc file that curl should use. You can only specify one
netrc file per invocation.

It will abide by \-\-netrc-optional if specified.

If --netrc-file is provided several times, the last set value will be used.

Example:
.nf
 curl --netrc-file netrc https://example.com
.fi

See also \fI-n, --netrc\fP, \fI-u, --user\fP and \fI-K, --config\fP. This option is mutually exclusive to \fI-n, --netrc\fP.
.IP "\-\-netrc-optional"
Similar to \fI\-n, \-\-netrc\fP, but this option makes the .netrc usage \fBoptional\fP
and not mandatory as the \-\-netrc option does.

Providing --netrc-optional multiple times has no extra effect.
Disable it again with --no-netrc-optional.

Example:
.nf
 curl --netrc-optional https://example.com
.fi

See also \fI--netrc-file\fP. This option is mutually exclusive to \fI-n, --netrc\fP.
.IP "\-n, \-\-netrc"
Makes curl scan the \fI.netrc\fP (\fI_netrc\fP on Windows) file in the user's home
directory for login name and password. This is typically used for FTP on
Unix. If used with HTTP, curl will enable user authentication. See
\fInetrc(5)\fP and \fIftp(1)\fP for details on the file format. Curl will not
complain if that file does not have the right permissions (it should be
neither world- nor group-readable). The environment variable "HOME" is used
to find the home directory.

A quick and simple example of how to setup a \fI.netrc\fP to allow curl to FTP to
the machine host.domain.com with user name 'myself' and password 'secret'
could look similar to:

.nf
 machine host.domain.com
 login myself
 password secret
.fi

Providing --netrc multiple times has no extra effect.
Disable it again with --no-netrc.

Example:
.nf
 curl --netrc https://example.com
.fi

See also \fI--netrc-file\fP, \fI-K, --config\fP and \fI-u, --user\fP.
.IP "\-:, \-\-next"
Tells curl to use a separate operation for the following URL and associated
options. This allows you to send several URL requests, each with their own
specific options, for example, such as different user names or custom requests
for each.

\-\-next will reset all local options and only global ones will have their
values survive over to the operation following the \-\-next instruction. Global
options include \fI\-v, \-\-verbose\fP, \fI\-\-trace\fP, \-\-trace-ascii and \fI\-\-fail-early\fP.

For example, you can do both a GET and a POST in a single command line:

.nf
 curl www1.example.com \-\-next \-d postthis www2.example.com
.fi

--next can be used several times in a command line

Examples:
.nf
 curl https://example.com --next -d postthis www2.example.com
 curl -I https://example.com --next https://example.net/
.fi

See also \fI-Z, --parallel\fP and \fI-K, --config\fP. Added in 7.36.0.
.IP "\-\-no-alpn"
(HTTPS) Disable the ALPN TLS extension. ALPN is enabled by default if libcurl was built
with an SSL library that supports ALPN. ALPN is used by a libcurl that supports
HTTP/2 to negotiate HTTP/2 support with the server during https sessions.

Providing --no-alpn multiple times has no extra effect.
Disable it again with --alpn.

Example:
.nf
 curl --no-alpn https://example.com
.fi

See also \fI--no-npn\fP and \fI--http2\fP. \fI--no-alpn\fP requires that the underlying libcurl was built to support TLS. Added in 7.36.0.
.IP "\-N, \-\-no-buffer"
Disables the buffering of the output stream. In normal work situations, curl
will use a standard buffered output stream that will have the effect that it
will output the data in chunks, not necessarily exactly when the data arrives.
Using this option will disable that buffering.

Providing --no-buffer multiple times has no extra effect.
Disable it again with --buffer.

Example:
.nf
 curl --no-buffer https://example.com
.fi

See also \fI-#, --progress-bar\fP.
.IP "\-\-no-clobber"
When used in conjunction with the \fI\-o, \-\-output\fP, \fI\-J, \-\-remote-header-name\fP,
\fI\-O, \-\-remote-name\fP, or \-\-remote-name-all options, curl avoids overwriting files
that already exist. Instead, a dot and a number gets appended to the name
of the file that would be created, up to filename.100 after which it will not
create any file.

Note that this is the negated option name documented.  You can thus use
\-\-clobber to enforce the clobbering, even if \-\-remote-header-name or \-J is
specified.

Providing --no-clobber multiple times has no extra effect.
Disable it again with --clobber.

Example:
.nf
 curl --no-clobber --output local/dir/file https://example.com
.fi

See also \fI-o, --output\fP and \fI-O, --remote-name\fP. Added in 7.83.0.
.IP "\-\-no-keepalive"
Disables the use of keepalive messages on the TCP connection. curl otherwise
enables them by default.

Note that this is the negated option name documented. You can thus use
\-\-keepalive to enforce keepalive.

Providing --no-keepalive multiple times has no extra effect.
Disable it again with --keepalive.

Example:
.nf
 curl --no-keepalive https://example.com
.fi

See also \fI--keepalive-time\fP.
.IP "\-\-no-npn"
(HTTPS) In curl 7.86.0 and later, curl never uses NPN.

Disable the NPN TLS extension. NPN is enabled by default if libcurl was built
with an SSL library that supports NPN. NPN is used by a libcurl that supports
HTTP/2 to negotiate HTTP/2 support with the server during https sessions.

Providing --no-npn multiple times has no extra effect.
Disable it again with --npn.

Example:
.nf
 curl --no-npn https://example.com
.fi

See also \fI--no-alpn\fP and \fI--http2\fP. \fI--no-npn\fP requires that the underlying libcurl was built to support TLS. Added in 7.36.0.
.IP "\-\-no-progress-meter"
Option to switch off the progress meter output without muting or otherwise
affecting warning and informational messages like \-\-silent does.

Note that this is the negated option name documented. You can thus use
\-\-progress-meter to enable the progress meter again.

Providing --no-progress-meter multiple times has no extra effect.
Disable it again with --progress-meter.

Example:
.nf
 curl --no-progress-meter -o store https://example.com
.fi

See also \fI-v, --verbose\fP and \fI-s, --silent\fP. Added in 7.67.0.
.IP "\-\-no-sessionid"
(TLS) Disable curl's use of SSL session-ID caching. By default all transfers are
done using the cache. Note that while nothing should ever get hurt by
attempting to reuse SSL session-IDs, there seem to be broken SSL
implementations in the wild that may require you to disable this in order for
you to succeed.

Note that this is the negated option name documented. You can thus use
\-\-sessionid to enforce session-ID caching.

Providing --no-sessionid multiple times has no extra effect.
Disable it again with --sessionid.

Example:
.nf
 curl --no-sessionid https://example.com
.fi

See also \fI-k, --insecure\fP.
.IP "\-\-noproxy <no-proxy-list>"
Comma-separated list of hosts for which not to use a proxy, if one is
specified. The only wildcard is a single * character, which matches all hosts,
and effectively disables the proxy. Each name in this list is matched as
either a domain which contains the hostname, or the hostname itself. For
example, local.com would match local.com, local.com:80, and www.local.com, but
not www.notlocal.com.

Since 7.53.0, This option overrides the environment variables that disable the
proxy ('no_proxy' and 'NO_PROXY'). If there's an environment variable
disabling a proxy, you can set the noproxy list to "" to override it.

If --noproxy is provided several times, the last set value will be used.

Example:
.nf
 curl --noproxy "www.example" https://example.com
.fi

See also \fI-x, --proxy\fP.
.IP "\-\-ntlm-wb"
(HTTP) Enables NTLM much in the style \-\-ntlm does, but hand over the authentication
to the separate binary ntlmauth application that is executed when needed.

Providing --ntlm-wb multiple times has no extra effect.

Example:
.nf
 curl --ntlm-wb -u user:password https://example.com
.fi

See also \fI--ntlm\fP and \fI--proxy-ntlm\fP.
.IP "\-\-ntlm"
(HTTP) Enables NTLM authentication. The NTLM authentication method was designed by
Microsoft and is used by IIS web servers. It is a proprietary protocol,
reverse-engineered by clever people and implemented in curl based on their
efforts. This kind of behavior should not be endorsed, you should encourage
everyone who uses NTLM to switch to a public and documented authentication
method instead, such as Digest.

If you want to enable NTLM for your proxy authentication, then use
\fI\-\-proxy-ntlm\fP.

If this option is used several times, only the first one is used.

Providing --ntlm multiple times has no extra effect.

Example:
.nf
 curl --ntlm -u user:password https://example.com
.fi

See also \fI--proxy-ntlm\fP. \fI--ntlm\fP requires that the underlying libcurl was built to support TLS. This option is mutually exclusive to \fI--basic\fP and \fI--negotiate\fP and \fI--digest\fP and \fI--anyauth\fP.
.IP "\-\-oauth2-bearer <token>"
(IMAP LDAP POP3 SMTP HTTP) Specify the Bearer Token for OAUTH 2.0 server authentication. The Bearer Token
is used in conjunction with the user name which can be specified as part of
the \-\-url or \-\-user options.

The Bearer Token and user name are formatted according to RFC 6750.

If --oauth2-bearer is provided several times, the last set value will be used.

Example:
.nf
 curl --oauth2-bearer "mF_9.B5f-4.1JqM" https://example.com
.fi

See also \fI--basic\fP, \fI--ntlm\fP and \fI--digest\fP. Added in 7.33.0.
.IP "\-\-output-dir <dir>"
This option specifies the directory in which files should be stored, when
\-\-remote-name or \-\-output are used.

The given output directory is used for all URLs and output options on the
command line, up until the first \fI\-:, \-\-next\fP.

If the specified target directory does not exist, the operation will fail
unless \-\-create-dirs is also used.

If --output-dir is provided several times, the last set value will be used.

Example:
.nf
 curl --output-dir "tmp" -O https://example.com
.fi

See also \fI-O, --remote-name\fP and \fI-J, --remote-header-name\fP. Added in 7.73.0.
.IP "\-o, \-\-output <file>"
Write output to <file> instead of stdout. If you are using {} or [] to fetch
multiple documents, you should quote the URL and you can use '#' followed by a
number in the <file> specifier. That variable will be replaced with the current
string for the URL being fetched. Like in:

.nf
 curl "http://{one,two}.example.com" \-o "file_#1.txt"
.fi

or use several variables like:

.nf
 curl "http://{site,host}.host[1-5].com" \-o "#1_#2"
.fi

You may use this option as many times as the number of URLs you have. For
example, if you specify two URLs on the same command line, you can use it like
this:

.nf
  curl \-o aa example.com \-o bb example.net
.fi

and the order of the \-o options and the URLs does not matter, just that the
first \-o is for the first URL and so on, so the above command line can also be
written as

.nf
  curl example.com example.net \-o aa \-o bb
.fi

See also the \-\-create-dirs option to create the local directories
dynamically. Specifying the output as '-' (a single dash) will force the
output to be done to stdout.

To suppress response bodies, you can redirect output to /dev/null:

.nf
  curl example.com \-o /dev/null
.fi

Or for Windows use nul:

.nf
  curl example.com \-o nul
.fi

--output can be used several times in a command line

Examples:
.nf
 curl -o file https://example.com
 curl "http://{one,two}.example.com" -o "file_#1.txt"
 curl "http://{site,host}.host[1-5].com" -o "#1_#2"
 curl -o file https://example.com -o file2 https://example.net
.fi

See also \fI-O, --remote-name\fP, \fI--remote-name-all\fP and \fI-J, --remote-header-name\fP.
.IP "\-\-parallel-immediate"
When doing parallel transfers, this option will instruct curl that it should
rather prefer opening up more connections in parallel at once rather than
waiting to see if new transfers can be added as multiplexed streams on another
connection.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Providing --parallel-immediate multiple times has no extra effect.
Disable it again with --no-parallel-immediate.

Example:
.nf
 curl --parallel-immediate -Z https://example.com -o file1 https://example.com -o file2
.fi

See also \fI-Z, --parallel\fP and \fI--parallel-max\fP. Added in 7.68.0.
.IP "\-\-parallel-max <num>"
When asked to do parallel transfers, using \fI\-Z, \-\-parallel\fP, this option controls
the maximum amount of transfers to do simultaneously.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

The default is 50.

If --parallel-max is provided several times, the last set value will be used.

Example:
.nf
 curl --parallel-max 100 -Z https://example.com ftp://example.com/
.fi

See also \fI-Z, --parallel\fP. Added in 7.66.0.
.IP "\-Z, \-\-parallel"
Makes curl perform its transfers in parallel as compared to the regular serial
manner.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Providing --parallel multiple times has no extra effect.
Disable it again with --no-parallel.

Example:
.nf
 curl --parallel https://example.com -o file1 https://example.com -o file2
.fi

See also \fI-:, --next\fP and \fI-v, --verbose\fP. Added in 7.66.0.
.IP "\-\-pass <phrase>"
(SSH TLS) Passphrase for the private key.

If --pass is provided several times, the last set value will be used.

Example:
.nf
 curl --pass secret --key file https://example.com
.fi

See also \fI--key\fP and \fI-u, --user\fP.
.IP "\-\-path-as-is"
Tell curl to not handle sequences of /../ or /./ in the given URL
path. Normally curl will squash or merge them according to standards but with
this option set you tell it not to do that.

Providing --path-as-is multiple times has no extra effect.
Disable it again with --no-path-as-is.

Example:
.nf
 curl --path-as-is https://example.com/../../etc/passwd
.fi

See also \fI--request-target\fP. Added in 7.42.0.
.IP "\-\-pinnedpubkey <hashes>"
(TLS) Tells curl to use the specified public key file (or hashes) to verify the
peer. This can be a path to a file which contains a single public key in PEM
or DER format, or any number of base64 encoded sha256 hashes preceded by
\(aqsha256//' and separated by ';'.

When negotiating a TLS or SSL connection, the server sends a certificate
indicating its identity. A public key is extracted from this certificate and
if it does not exactly match the public key provided to this option, curl will
abort the connection before sending or receiving any data.

PEM/DER support:

7.39.0: OpenSSL, GnuTLS and GSKit

7.43.0: NSS and wolfSSL

7.47.0: mbedtls

sha256 support:

7.44.0: OpenSSL, GnuTLS, NSS and wolfSSL

7.47.0: mbedtls

Other SSL backends not supported.

If --pinnedpubkey is provided several times, the last set value will be used.

Examples:
.nf
 curl --pinnedpubkey keyfile https://example.com
 curl --pinnedpubkey 'sha256//ce118b51897f4452dc' https://example.com
.fi

See also \fI--hostpubsha256\fP. Added in 7.39.0.
.IP "\-\-post301"
(HTTP) Tells curl to respect RFC 7231/6.4.2 and not convert POST requests into GET
requests when following a 301 redirection. The non-RFC behavior is ubiquitous
in web browsers, so curl does the conversion by default to maintain
consistency. However, a server may require a POST to remain a POST after such
a redirection. This option is meaningful only when using \fI\-L, \-\-location\fP.

Providing --post301 multiple times has no extra effect.
Disable it again with --no-post301.

Example:
.nf
 curl --post301 --location -d "data" https://example.com
.fi

See also \fI--post302\fP, \fI--post303\fP and \fI-L, --location\fP.
.IP "\-\-post302"
(HTTP) Tells curl to respect RFC 7231/6.4.3 and not convert POST requests into GET
requests when following a 302 redirection. The non-RFC behavior is ubiquitous
in web browsers, so curl does the conversion by default to maintain
consistency. However, a server may require a POST to remain a POST after such
a redirection. This option is meaningful only when using \fI\-L, \-\-location\fP.

Providing --post302 multiple times has no extra effect.
Disable it again with --no-post302.

Example:
.nf
 curl --post302 --location -d "data" https://example.com
.fi

See also \fI--post301\fP, \fI--post303\fP and \fI-L, --location\fP.
.IP "\-\-post303"
(HTTP) Tells curl to violate RFC 7231/6.4.4 and not convert POST requests into GET
requests when following 303 redirections. A server may require a POST to
remain a POST after a 303 redirection. This option is meaningful only when
using \fI\-L, \-\-location\fP.

Providing --post303 multiple times has no extra effect.
Disable it again with --no-post303.

Example:
.nf
 curl --post303 --location -d "data" https://example.com
.fi

See also \fI--post302\fP, \fI--post301\fP and \fI-L, --location\fP.
.IP "\-\-preproxy [protocol://]host[:port]"
Use the specified SOCKS proxy before connecting to an HTTP or HTTPS \fI\-x, \-\-proxy\fP. In
such a case curl first connects to the SOCKS proxy and then connects (through
SOCKS) to the HTTP or HTTPS proxy. Hence pre proxy.

The pre proxy string should be specified with a protocol:// prefix to specify
alternative proxy protocols. Use socks4://, socks4a://, socks5:// or
socks5h:// to request the specific SOCKS version to be used. No protocol
specified will make curl default to SOCKS4.

If the port number is not specified in the proxy string, it is assumed to be
1080.

User and password that might be provided in the proxy string are URL decoded
by curl. This allows you to pass in special characters such as @ by using %40
or pass in a colon with %3a.

If --preproxy is provided several times, the last set value will be used.

Example:
.nf
 curl --preproxy socks5://proxy.example -x http://http.example https://example.com
.fi

See also \fI-x, --proxy\fP and \fI--socks5\fP. Added in 7.52.0.
.IP "\-#, \-\-progress-bar"
Make curl display transfer progress as a simple progress bar instead of the
standard, more informational, meter.

This progress bar draws a single line of '#' characters across the screen and
shows a percentage if the transfer size is known. For transfers without a
known size, there will be space ship (-=o=-) that moves back and forth but
only while data is being transferred, with a set of flying hash sign symbols on
top.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Providing --progress-bar multiple times has no extra effect.
Disable it again with --no-progress-bar.

Example:
.nf
 curl -# -O https://example.com
.fi

See also \fI--styled-output\fP.
.IP "\-\-proto-default <protocol>"
Tells curl to use \fIprotocol\fP for any URL missing a scheme name.

An unknown or unsupported protocol causes error
\fICURLE_UNSUPPORTED_PROTOCOL\fP (1).

This option does not change the default proxy protocol (http).

Without this option set, curl guesses protocol based on the host name, see
\-\-url for details.

If --proto-default is provided several times, the last set value will be used.

Example:
.nf
 curl --proto-default https ftp.example.com
.fi

See also \fI--proto\fP and \fI--proto-redir\fP. Added in 7.45.0.
.IP "\-\-proto-redir <protocols>"
Tells curl to limit what protocols it may use on redirect. Protocols denied by
\-\-proto are not overridden by this option. See \-\-proto for how protocols are
represented.

Example, allow only HTTP and HTTPS on redirect:

.nf
 curl \-\-proto-redir \-all,http,https http://example.com
.fi

By default curl will only allow HTTP, HTTPS, FTP and FTPS on redirect (since
7.65.2). Specifying \fIall\fP or \fI+all\fP enables all protocols on redirects, which
is not good for security.

If --proto-redir is provided several times, the last set value will be used.

Example:
.nf
 curl --proto-redir =http,https https://example.com
.fi

See also \fI--proto\fP.
.IP "\-\-proto <protocols>"
Tells curl to limit what protocols it may use for transfers. Protocols are
evaluated left to right, are comma separated, and are each a protocol name or
\(aqall', optionally prefixed by zero or more modifiers. Available modifiers are:
.RS
.TP 3
.B +
Permit this protocol in addition to protocols already permitted (this is
the default if no modifier is used).
.TP
.B \-
Deny this protocol, removing it from the list of protocols already permitted.
.TP
.B =
Permit only this protocol (ignoring the list already permitted), though
subject to later modification by subsequent entries in the comma separated
list.
.RE
.IP
For example:
.RS
.TP 15
.B \fI\-\-proto\fP \-ftps
uses the default protocols, but disables ftps
.TP
.B  \fI\-\-proto\fP \-all,https,+http
only enables http and https
.TP
.B \fI\-\-proto\fP =http,https
also only enables http and https
.RE
.IP
Unknown and disabled protocols produce a warning. This allows scripts to
safely rely on being able to disable potentially dangerous protocols, without
relying upon support for that protocol being built into curl to avoid an error.

This option can be used multiple times, in which case the effect is the same
as concatenating the protocols into one instance of the option.

If --proto is provided several times, the last set value will be used.

Example:
.nf
 curl --proto =http,https,sftp https://example.com
.fi

See also \fI--proto-redir\fP and \fI--proto-default\fP.
.IP "\-\-proxy-anyauth"
Tells curl to pick a suitable authentication method when communicating with
the given HTTP proxy. This might cause an extra request/response round-trip.

Providing --proxy-anyauth multiple times has no extra effect.

Example:
.nf
 curl --proxy-anyauth --proxy-user user:passwd -x proxy https://example.com
.fi

See also \fI-x, --proxy\fP, \fI--proxy-basic\fP and \fI--proxy-digest\fP.
.IP "\-\-proxy-basic"
Tells curl to use HTTP Basic authentication when communicating with the given
proxy. Use \-\-basic for enabling HTTP Basic with a remote host. Basic is the
default authentication method curl uses with proxies.

Providing --proxy-basic multiple times has no extra effect.

Example:
.nf
 curl --proxy-basic --proxy-user user:passwd -x proxy https://example.com
.fi

See also \fI-x, --proxy\fP, \fI--proxy-anyauth\fP and \fI--proxy-digest\fP.
.IP "\-\-proxy-cacert <file>"
Same as \-\-cacert but used in HTTPS proxy context.

If --proxy-cacert is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-cacert CA-file.txt -x https://proxy https://example.com
.fi

See also \fI--proxy-capath\fP, \fI--cacert\fP, \fI--capath\fP and \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-\-proxy-capath <dir>"
Same as \-\-capath but used in HTTPS proxy context.

If --proxy-capath is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-capath /local/directory -x https://proxy https://example.com
.fi

See also \fI--proxy-cacert\fP, \fI-x, --proxy\fP and \fI--capath\fP. Added in 7.52.0.
.IP "\-\-proxy-cert-type <type>"
Same as \-\-cert-type but used in HTTPS proxy context.

If --proxy-cert-type is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-cert-type PEM --proxy-cert file -x https://proxy https://example.com
.fi

See also \fI--proxy-cert\fP. Added in 7.52.0.
.IP "\-\-proxy-cert <cert[:passwd]>"
Same as \-\-cert but used in HTTPS proxy context.

If --proxy-cert is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-cert file -x https://proxy https://example.com
.fi

See also \fI--proxy-cert-type\fP. Added in 7.52.0.
.IP "\-\-proxy-ciphers <list>"
Same as \-\-ciphers but used in HTTPS proxy context.

If --proxy-ciphers is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-ciphers ECDHE-ECDSA-AES256-CCM8 -x https://proxy https://example.com
.fi

See also \fI--ciphers\fP, \fI--curves\fP and \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-\-proxy-crlfile <file>"
Same as \-\-crlfile but used in HTTPS proxy context.

If --proxy-crlfile is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-crlfile rejects.txt -x https://proxy https://example.com
.fi

See also \fI--crlfile\fP and \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-\-proxy-digest"
Tells curl to use HTTP Digest authentication when communicating with the given
proxy. Use \-\-digest for enabling HTTP Digest with a remote host.

Providing --proxy-digest multiple times has no extra effect.

Example:
.nf
 curl --proxy-digest --proxy-user user:passwd -x proxy https://example.com
.fi

See also \fI-x, --proxy\fP, \fI--proxy-anyauth\fP and \fI--proxy-basic\fP.
.IP "\-\-proxy-header <header/@file>"
(HTTP) Extra header to include in the request when sending HTTP to a proxy. You may
specify any number of extra headers. This is the equivalent option to \-\-header
but is for proxy communication only like in CONNECT requests when you want a
separate header sent to the proxy to what is sent to the actual remote host.

curl will make sure that each header you add/replace is sent with the proper
end-of-line marker, you should thus \fBnot\fP add that as a part of the header
content: do not add newlines or carriage returns, they will only mess things
up for you.

Headers specified with this option will not be included in requests that curl
knows will not be sent to a proxy.

Starting in 7.55.0, this option can take an argument in @filename style, which
then adds a header for each line in the input file. Using @- will make curl
read the header file from stdin.

This option can be used multiple times to add/replace/remove multiple headers.

--proxy-header can be used several times in a command line

Examples:
.nf
 curl --proxy-header "X-First-Name: Joe" -x http://proxy https://example.com
 curl --proxy-header "User-Agent: surprise" -x http://proxy https://example.com
 curl --proxy-header "Host:" -x http://proxy https://example.com
.fi

See also \fI-x, --proxy\fP. Added in 7.37.0.
.IP "\-\-proxy-insecure"
Same as \-\-insecure but used in HTTPS proxy context.

Providing --proxy-insecure multiple times has no extra effect.
Disable it again with --no-proxy-insecure.

Example:
.nf
 curl --proxy-insecure -x https://proxy https://example.com
.fi

See also \fI-x, --proxy\fP and \fI-k, --insecure\fP. Added in 7.52.0.
.IP "\-\-proxy-key-type <type>"
Same as \-\-key-type but used in HTTPS proxy context.

If --proxy-key-type is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-key-type DER --proxy-key here -x https://proxy https://example.com
.fi

See also \fI--proxy-key\fP and \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-\-proxy-key <key>"
Same as \-\-key but used in HTTPS proxy context.

If --proxy-key is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-key here -x https://proxy https://example.com
.fi

See also \fI--proxy-key-type\fP and \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-\-proxy-negotiate"
Tells curl to use HTTP Negotiate (SPNEGO) authentication when communicating
with the given proxy. Use \-\-negotiate for enabling HTTP Negotiate (SPNEGO)
with a remote host.

Providing --proxy-negotiate multiple times has no extra effect.

Example:
.nf
 curl --proxy-negotiate --proxy-user user:passwd -x proxy https://example.com
.fi

See also \fI--proxy-anyauth\fP and \fI--proxy-basic\fP.
.IP "\-\-proxy-ntlm"
Tells curl to use HTTP NTLM authentication when communicating with the given
proxy. Use \-\-ntlm for enabling NTLM with a remote host.

Providing --proxy-ntlm multiple times has no extra effect.

Example:
.nf
 curl --proxy-ntlm --proxy-user user:passwd -x http://proxy https://example.com
.fi

See also \fI--proxy-negotiate\fP and \fI--proxy-anyauth\fP.
.IP "\-\-proxy-pass <phrase>"
Same as \-\-pass but used in HTTPS proxy context.

If --proxy-pass is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-pass secret --proxy-key here -x https://proxy https://example.com
.fi

See also \fI-x, --proxy\fP and \fI--proxy-key\fP. Added in 7.52.0.
.IP "\-\-proxy-pinnedpubkey <hashes>"
(TLS) Tells curl to use the specified public key file (or hashes) to verify the
proxy. This can be a path to a file which contains a single public key in PEM
or DER format, or any number of base64 encoded sha256 hashes preceded by
\(aqsha256//' and separated by ';'.

When negotiating a TLS or SSL connection, the server sends a certificate
indicating its identity. A public key is extracted from this certificate and
if it does not exactly match the public key provided to this option, curl will
abort the connection before sending or receiving any data.

If --proxy-pinnedpubkey is provided several times, the last set value will be used.

Examples:
.nf
 curl --proxy-pinnedpubkey keyfile https://example.com
 curl --proxy-pinnedpubkey 'sha256//ce118b51897f4452dc' https://example.com
.fi

See also \fI--pinnedpubkey\fP and \fI-x, --proxy\fP. Added in 7.59.0.
.IP "\-\-proxy-service-name <name>"
This option allows you to change the service name for proxy negotiation.

If --proxy-service-name is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-service-name "shrubbery" -x proxy https://example.com
.fi

See also \fI--service-name\fP and \fI-x, --proxy\fP. Added in 7.43.0.
.IP "\-\-proxy-ssl-allow-beast"
Same as \-\-ssl-allow-beast but used in HTTPS proxy context.

Providing --proxy-ssl-allow-beast multiple times has no extra effect.
Disable it again with --no-proxy-ssl-allow-beast.

Example:
.nf
 curl --proxy-ssl-allow-beast -x https://proxy https://example.com
.fi

See also \fI--ssl-allow-beast\fP and \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-\-proxy-ssl-auto-client-cert"
Same as \-\-ssl-auto-client-cert but used in HTTPS proxy context.

Providing --proxy-ssl-auto-client-cert multiple times has no extra effect.
Disable it again with --no-proxy-ssl-auto-client-cert.

Example:
.nf
 curl --proxy-ssl-auto-client-cert -x https://proxy https://example.com
.fi

See also \fI--ssl-auto-client-cert\fP and \fI-x, --proxy\fP. Added in 7.77.0.
.IP "\-\-proxy-tls13-ciphers <ciphersuite list>"
(TLS) Specifies which cipher suites to use in the connection to your HTTPS proxy
when it negotiates TLS 1.3. The list of ciphers suites must specify valid
ciphers. Read up on TLS 1.3 cipher suite details on this URL:

.nf
 https://curl.se/docs/ssl-ciphers.html
.fi

This option is currently used only when curl is built to use OpenSSL 1.1.1 or
later. If you are using a different SSL backend you can try setting TLS 1.3
cipher suites by using the \-\-proxy-ciphers option.

If --proxy-tls13-ciphers is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-tls13-ciphers TLS_AES_128_GCM_SHA256 -x proxy https://example.com
.fi

See also \fI--tls13-ciphers\fP and \fI--curves\fP. Added in 7.61.0.
.IP "\-\-proxy-tlsauthtype <type>"
Same as \-\-tlsauthtype but used in HTTPS proxy context.

If --proxy-tlsauthtype is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-tlsauthtype SRP -x https://proxy https://example.com
.fi

See also \fI-x, --proxy\fP and \fI--proxy-tlsuser\fP. Added in 7.52.0.
.IP "\-\-proxy-tlspassword <string>"
Same as \-\-tlspassword but used in HTTPS proxy context.

If --proxy-tlspassword is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-tlspassword passwd -x https://proxy https://example.com
.fi

See also \fI-x, --proxy\fP and \fI--proxy-tlsuser\fP. Added in 7.52.0.
.IP "\-\-proxy-tlsuser <name>"
Same as \-\-tlsuser but used in HTTPS proxy context.

If --proxy-tlsuser is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-tlsuser smith -x https://proxy https://example.com
.fi

See also \fI-x, --proxy\fP and \fI--proxy-tlspassword\fP. Added in 7.52.0.
.IP "\-\-proxy-tlsv1"
Same as \-\-tlsv1 but used in HTTPS proxy context.

Providing --proxy-tlsv1 multiple times has no extra effect.

Example:
.nf
 curl --proxy-tlsv1 -x https://proxy https://example.com
.fi

See also \fI-x, --proxy\fP. Added in 7.52.0.
.IP "\-U, \-\-proxy-user <user:password>"
Specify the user name and password to use for proxy authentication.

If you use a Windows SSPI-enabled curl binary and do either Negotiate or NTLM
authentication then you can tell curl to select the user name and password
from your environment by specifying a single colon with this option: "-U :".

On systems where it works, curl will hide the given option argument from
process listings. This is not enough to protect credentials from possibly
getting seen by other users on the same system as they will still be visible
for a moment before cleared. Such sensitive data should be retrieved from a
file instead or similar and never used in clear text in a command line.

If --proxy-user is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy-user name:pwd -x proxy https://example.com
.fi

See also \fI--proxy-pass\fP.
.IP "\-x, \-\-proxy [protocol://]host[:port]"
Use the specified proxy.

The proxy string can be specified with a protocol:// prefix. No protocol
specified or http:// will be treated as HTTP proxy. Use socks4://, socks4a://,
socks5:// or socks5h:// to request a specific SOCKS version to be used.


Unix domain sockets are supported for socks proxy. Set localhost for the host
part. e.g. socks5h://localhost/path/to/socket.sock

HTTPS proxy support via https:// protocol prefix was added in 7.52.0 for
OpenSSL, GnuTLS and NSS.

Unrecognized and unsupported proxy protocols cause an error since 7.52.0.
Prior versions may ignore the protocol and use http:// instead.

If the port number is not specified in the proxy string, it is assumed to be
1080.

This option overrides existing environment variables that set the proxy to
use. If there's an environment variable setting a proxy, you can set proxy to
\(dq" to override it.

All operations that are performed over an HTTP proxy will transparently be
converted to HTTP. It means that certain protocol specific operations might
not be available. This is not the case if you can tunnel through the proxy, as
one with the \-\-proxytunnel option.

User and password that might be provided in the proxy string are URL decoded
by curl. This allows you to pass in special characters such as @ by using %40
or pass in a colon with %3a.

The proxy host can be specified the same way as the proxy environment
variables, including the protocol prefix (http://) and the embedded user +
password.

If --proxy is provided several times, the last set value will be used.

Example:
.nf
 curl --proxy http://proxy.example https://example.com
.fi

See also \fI--socks5\fP and \fI--proxy-basic\fP.
.IP "\-\-proxy1.0 <host[:port]>"
Use the specified HTTP 1.0 proxy. If the port number is not specified, it is
assumed at port 1080.

The only difference between this and the HTTP proxy option \fI\-x, \-\-proxy\fP, is that
attempts to use CONNECT through the proxy will specify an HTTP 1.0 protocol
instead of the default HTTP 1.1.

Providing --proxy1.0 multiple times has no extra effect.

Example:
.nf
 curl --proxy1.0 -x http://proxy https://example.com
.fi

See also \fI-x, --proxy\fP, \fI--socks5\fP and \fI--preproxy\fP.
.IP "\-p, \-\-proxytunnel"
When an HTTP proxy is used \fI\-x, \-\-proxy\fP, this option will make curl tunnel through
the proxy. The tunnel approach is made with the HTTP proxy CONNECT request and
requires that the proxy allows direct connect to the remote port number curl
wants to tunnel through to.

To suppress proxy CONNECT response headers when curl is set to output headers
use \fI\-\-suppress-connect-headers\fP.

Providing --proxytunnel multiple times has no extra effect.
Disable it again with --no-proxytunnel.

Example:
.nf
 curl --proxytunnel -x http://proxy https://example.com
.fi

See also \fI-x, --proxy\fP.
.IP "\-\-pubkey <key>"
(SFTP SCP) Public key file name. Allows you to provide your public key in this separate
file.

(As of 7.39.0, curl attempts to automatically extract the public key from the
private key file, so passing this option is generally not required. Note that
this public key extraction requires libcurl to be linked against a copy of
libssh2 1.2.8 or higher that is itself linked against OpenSSL.)

If --pubkey is provided several times, the last set value will be used.

Example:
.nf
 curl --pubkey file.pub sftp://example.com/
.fi

See also \fI--pass\fP.
.IP "\-Q, \-\-quote <command>"
(FTP SFTP) Send an arbitrary command to the remote FTP or SFTP server. Quote commands are
sent BEFORE the transfer takes place (just after the initial PWD command in an
FTP transfer, to be exact). To make commands take place after a successful
transfer, prefix them with a dash '-'.

(FTP only) To make commands be sent after curl has changed the working
directory, just before the file transfer command(s), prefix the command with a
\(aq+'. This is not performed when a directory listing is performed.

You may specify any number of commands.

By default curl will stop at first failure. To make curl continue even if the
command fails, prefix the command with an asterisk (*). Otherwise, if the
server returns failure for one of the commands, the entire operation will be
aborted.

You must send syntactically correct FTP commands as RFC 959 defines to FTP
servers, or one of the commands listed below to SFTP servers.

This option can be used multiple times.

SFTP is a binary protocol. Unlike for FTP, curl interprets SFTP quote commands
itself before sending them to the server. File names may be quoted
shell-style to embed spaces or special characters. Following is the list of
all supported SFTP quote commands:
.RS
.IP "atime date file"
The atime command sets the last access time of the file named by the file
operand. The <date expression> can be all sorts of date strings, see the
\fIcurl_getdate(3)\fP man page for date expression details. (Added in 7.73.0)
.IP "chgrp group file"
The chgrp command sets the group ID of the file named by the file operand to
the group ID specified by the group operand. The group operand is a decimal
integer group ID.
.IP "chmod mode file"
The chmod command modifies the file mode bits of the specified file. The
mode operand is an octal integer mode number.
.IP "chown user file"
The chown command sets the owner of the file named by the file operand to the
user ID specified by the user operand. The user operand is a decimal
integer user ID.
.IP "ln source_file target_file"
The ln and symlink commands create a symbolic link at the target_file location
pointing to the source_file location.
.IP "mkdir directory_name"
The mkdir command creates the directory named by the directory_name operand.
.IP "mtime date file"
The mtime command sets the last modification time of the file named by the
file operand. The <date expression> can be all sorts of date strings, see the
\fIcurl_getdate(3)\fP man page for date expression details. (Added in 7.73.0)
.IP "pwd"
The pwd command returns the absolute pathname of the current working directory.
.IP "rename source target"
The rename command renames the file or directory named by the source
operand to the destination path named by the target operand.
.IP "rm file"
The rm command removes the file specified by the file operand.
.IP "rmdir directory"
The rmdir command removes the directory entry specified by the directory
operand, provided it is empty.
.IP "symlink source_file target_file"
See ln.
.RE

--quote can be used several times in a command line

Example:
.nf
 curl --quote "DELE file" ftp://example.com/foo
.fi

See also \fI-X, --request\fP.
.IP "\-\-random-file <file>"
Deprecated option. This option is ignored by curl since 7.84.0. Prior to that
it only had an effect on curl if built to use old versions of OpenSSL.

Specify the path name to file containing what will be considered as random
data. The data may be used to seed the random engine for SSL connections.

If --random-file is provided several times, the last set value will be used.

Example:
.nf
 curl --random-file rubbish https://example.com
.fi

See also \fI--egd-file\fP.
.IP "\-r, \-\-range <range>"
(HTTP FTP SFTP FILE) Retrieve a byte range (i.e. a partial document) from an HTTP/1.1, FTP or SFTP
server or a local FILE. Ranges can be specified in a number of ways.
.RS
.TP 10
.B 0-499
specifies the first 500 bytes
.TP
.B 500-999
specifies the second 500 bytes
.TP
.B \-500
specifies the last 500 bytes
.TP
.B 9500-
specifies the bytes from offset 9500 and forward
.TP
.B 0-0,-1
specifies the first and last byte only(*)(HTTP)
.TP
.B 100-199,500-599
specifies two separate 100-byte ranges(*) (HTTP)
.RE
.IP
(*) = NOTE that this will cause the server to reply with a multipart
response, which will be returned as-is by curl! Parsing or otherwise
transforming this response is the responsibility of the caller.

Only digit characters (0-9) are valid in the 'start' and 'stop' fields of the
\(aqstart-stop' range syntax. If a non-digit character is given in the range,
the server's response will be unspecified, depending on the server's
configuration.

You should also be aware that many HTTP/1.1 servers do not have this feature
enabled, so that when you attempt to get a range, you will instead get the
whole document.

FTP and SFTP range downloads only support the simple 'start-stop' syntax
(optionally with one of the numbers omitted). FTP use depends on the extended
FTP command SIZE.

If --range is provided several times, the last set value will be used.

Example:
.nf
 curl --range 22-44 https://example.com
.fi

See also \fI-C, --continue-at\fP and \fI-a, --append\fP.
.IP "\-\-rate <max request rate>"
Specify the maximum transfer frequency you allow curl to use \- in number of
transfer starts per time unit (sometimes called request rate). Without this
option, curl will start the next transfer as fast as possible.

If given several URLs and a transfer completes faster than the allowed rate,
curl will wait until the next transfer is started to maintain the requested
rate. This option has no effect when \-\-parallel is used.

The request rate is provided as "N/U" where N is an integer number and U is a
time unit. Supported units are 's' (second), 'm' (minute), 'h' (hour) and 'd'
/(day, as in a 24 hour unit). The default time unit, if no "/U" is provided,
is number of transfers per hour.

If curl is told to allow 10 requests per minute, it will not start the next
request until 6 seconds have elapsed since the previous transfer was started.

This function uses millisecond resolution. If the allowed frequency is set
more than 1000 per second, it will instead run unrestricted.

When retrying transfers, enabled with \fI\-\-retry\fP, the separate retry delay logic
is used and not this setting.

If --rate is provided several times, the last set value will be used.

Examples:
.nf
 curl --rate 2/s https://example.com
 curl --rate 3/h https://example.com
 curl --rate 14/m https://example.com
.fi

See also \fI--limit-rate\fP and \fI--retry-delay\fP. Added in 7.84.0.
.IP "\-\-raw"
(HTTP) When used, it disables all internal HTTP decoding of content or transfer
encodings and instead makes them passed on unaltered, raw.

Providing --raw multiple times has no extra effect.
Disable it again with --no-raw.

Example:
.nf
 curl --raw https://example.com
.fi

See also \fI--tr-encoding\fP.
.IP "\-e, \-\-referer <URL>"
(HTTP) Sends the "Referrer Page" information to the HTTP server. This can also be set
with the \-\-header flag of course. When used with \-\-location you can append
\(dq;auto" to the \-\-referer URL to make curl automatically set the previous URL
when it follows a Location: header. The ";auto" string can be used alone,
even if you do not set an initial \fI\-e, \-\-referer\fP.

If --referer is provided several times, the last set value will be used.

Examples:
.nf
 curl --referer "https://fake.example" https://example.com
 curl --referer "https://fake.example;auto" -L https://example.com
 curl --referer ";auto" -L https://example.com
.fi

See also \fI-A, --user-agent\fP and \fI-H, --header\fP.
.IP "\-J, \-\-remote-header-name"
(HTTP) This option tells the \-\-remote-name option to use the server-specified
Content-Disposition filename instead of extracting a filename from the URL. If
the server-provided file name contains a path, that will be stripped off
before the file name is used.

The file is saved in the current directory, or in the directory specified with
\fI\-\-output-dir\fP.

If the server specifies a file name and a file with that name already exists
in the destination directory, it will not be overwritten and an error will
occur. If the server does not specify a file name then this option has no
effect.

There's no attempt to decode %-sequences (yet) in the provided file name, so
this option may provide you with rather unexpected file names.

\fBWARNING\fP: Exercise judicious use of this option, especially on Windows. A
rogue server could send you the name of a DLL or other file that could be
loaded automatically by Windows or some third party software.

Providing --remote-header-name multiple times has no extra effect.
Disable it again with --no-remote-header-name.

Example:
.nf
 curl -OJ https://example.com/file
.fi

See also \fI-O, --remote-name\fP.
.IP "\-\-remote-name-all"
This option changes the default action for all given URLs to be dealt with as
if \-\-remote-name were used for each one. So if you want to disable that for a
specific URL after \-\-remote-name-all has been used, you must use "-o \-" or
\-\-no-remote-name.

Providing --remote-name-all multiple times has no extra effect.
Disable it again with --no-remote-name-all.

Example:
.nf
 curl --remote-name-all ftp://example.com/file1 ftp://example.com/file2
.fi

See also \fI-O, --remote-name\fP.
.IP "\-O, \-\-remote-name"
Write output to a local file named like the remote file we get. (Only the file
part of the remote file is used, the path is cut off.)

The file will be saved in the current working directory. If you want the file
saved in a different directory, make sure you change the current working
directory before invoking curl with this option or use \fI\-\-output-dir\fP.

The remote file name to use for saving is extracted from the given URL,
nothing else, and if it already exists it will be overwritten. If you want the
server to be able to choose the file name refer to \-\-remote-header-name which
can be used in addition to this option. If the server chooses a file name and
that name already exists it will not be overwritten.

There is no URL decoding done on the file name. If it has %20 or other URL
encoded parts of the name, they will end up as-is as file name.

You may use this option as many times as the number of URLs you have.

--remote-name can be used several times in a command line

Example:
.nf
 curl -O https://example.com/filename
.fi

See also \fI--remote-name-all\fP, \fI--output-dir\fP and \fI-J, --remote-header-name\fP.
.IP "\-R, \-\-remote-time"
When used, this will make curl attempt to figure out the timestamp of the
remote file, and if that is available make the local file get that same
timestamp.

Providing --remote-time multiple times has no extra effect.
Disable it again with --no-remote-time.

Example:
.nf
 curl --remote-time -o foo https://example.com
.fi

See also \fI-O, --remote-name\fP and \fI-z, --time-cond\fP.
.IP "\-\-remove-on-error"
When curl returns an error when told to save output in a local file, this
option removes that saved file before exiting. This prevents curl from
leaving a partial file in the case of an error during transfer.

If the output is not a file, this option has no effect.

Providing --remove-on-error multiple times has no extra effect.
Disable it again with --no-remove-on-error.

Example:
.nf
 curl --remove-on-error -o output https://example.com
.fi

See also \fI-f, --fail\fP. Added in 7.83.0.
.IP "\-\-request-target <path>"
(HTTP) Tells curl to use an alternative "target" (path) instead of using the path as
provided in the URL. Particularly useful when wanting to issue HTTP requests
without leading slash or other data that does not follow the regular URL
pattern, like "OPTIONS *".

If --request-target is provided several times, the last set value will be used.

Example:
.nf
 curl --request-target "*" -X OPTIONS https://example.com
.fi

See also \fI-X, --request\fP. Added in 7.55.0.
.IP "\-X, \-\-request <method>"
(HTTP) Specifies a custom request method to use when communicating with the
HTTP server. The specified request method will be used instead of the method
otherwise used (which defaults to GET). Read the HTTP 1.1 specification for
details and explanations. Common additional HTTP requests include PUT and
DELETE, but related technologies like WebDAV offers PROPFIND, COPY, MOVE and
more.

Normally you do not need this option. All sorts of GET, HEAD, POST and PUT
requests are rather invoked by using dedicated command line options.

This option only changes the actual word used in the HTTP request, it does not
alter the way curl behaves. So for example if you want to make a proper HEAD
request, using \-X HEAD will not suffice. You need to use the \-\-head option.

The method string you set with \-\-request will be used for all requests, which
if you for example use \-\-location may cause unintended side-effects when curl
does not change request method according to the HTTP 30x response codes \- and
similar.

(FTP)
Specifies a custom FTP command to use instead of LIST when doing file lists
with FTP.

(POP3)
Specifies a custom POP3 command to use instead of LIST or RETR.


(IMAP)
Specifies a custom IMAP command to use instead of LIST. (Added in 7.30.0)

(SMTP)
Specifies a custom SMTP command to use instead of HELP or VRFY. (Added in 7.34.0)

If --request is provided several times, the last set value will be used.

Examples:
.nf
 curl -X "DELETE" https://example.com
 curl -X NLST ftp://example.com/
.fi

See also \fI--request-target\fP.
.IP "\-\-resolve <[+]host:port:addr[,addr]...>"
Provide a custom address for a specific host and port pair. Using this, you
can make the curl requests(s) use a specified address and prevent the
otherwise normally resolved address to be used. Consider it a sort of
/etc/hosts alternative provided on the command line. The port number should be
the number used for the specific protocol the host will be used for. It means
you need several entries if you want to provide address for the same host but
different ports.

By specifying '*' as host you can tell curl to resolve any host and specific
port pair to the specified address. Wildcard is resolved last so any \-\-resolve
with a specific host and port will be used first.

The provided address set by this option will be used even if \-\-ipv4 or \-\-ipv6
is set to make curl use another IP version.

By prefixing the host with a '+' you can make the entry time out after curl's
default timeout (1 minute). Note that this will only make sense for long
running parallel transfers with a lot of files. In such cases, if this option
is used curl will try to resolve the host as it normally would once the
timeout has expired.

Support for providing the IP address within [brackets] was added in 7.57.0.

Support for providing multiple IP addresses per entry was added in 7.59.0.

Support for resolving with wildcard was added in 7.64.0.

Support for the '+' prefix was was added in 7.75.0.

This option can be used many times to add many host names to resolve.

--resolve can be used several times in a command line

Example:
.nf
 curl --resolve example.com:443:127.0.0.1 https://example.com
.fi

See also \fI--connect-to\fP and \fI--alt-svc\fP.
.IP "\-\-retry-all-errors"
Retry on any error. This option is used together with \fI\-\-retry\fP.

This option is the "sledgehammer" of retrying. Do not use this option by
default (eg in curlrc), there may be unintended consequences such as sending or
receiving duplicate data. Do not use with redirected input or output. You'd be
much better off handling your unique problems in shell script. Please read the
example below.

\fBWARNING\fP: For server compatibility curl attempts to retry failed flaky
transfers as close as possible to how they were started, but this is not
possible with redirected input or output. For example, before retrying it
removes output data from a failed partial transfer that was written to an
output file. However this is not true of data redirected to a | pipe or >
file, which are not reset. We strongly suggest you do not parse or record
output via redirect in combination with this option, since you may receive
duplicate data.

By default curl will not error on an HTTP response code that indicates an HTTP
error, if the transfer was successful. For example, if a server replies 404
Not Found and the reply is fully received then that is not an error. When
\-\-retry is used then curl will retry on some HTTP response codes that indicate
transient HTTP errors, but that does not include most 4xx response codes such
as 404. If you want to retry on all response codes that indicate HTTP errors
(4xx and 5xx) then combine with \fI\-f, \-\-fail\fP.

Providing --retry-all-errors multiple times has no extra effect.
Disable it again with --no-retry-all-errors.

Example:
.nf
 curl --retry 5 --retry-all-errors https://example.com
.fi

See also \fI--retry\fP. Added in 7.71.0.
.IP "\-\-retry-connrefused"
In addition to the other conditions, consider ECONNREFUSED as a transient
error too for \fI\-\-retry\fP. This option is used together with \-\-retry.

Providing --retry-connrefused multiple times has no extra effect.
Disable it again with --no-retry-connrefused.

Example:
.nf
 curl --retry-connrefused --retry https://example.com
.fi

See also \fI--retry\fP and \fI--retry-all-errors\fP. Added in 7.52.0.
.IP "\-\-retry-delay <seconds>"
Make curl sleep this amount of time before each retry when a transfer has
failed with a transient error (it changes the default backoff time algorithm
between retries). This option is only interesting if \-\-retry is also
used. Setting this delay to zero will make curl use the default backoff time.

If --retry-delay is provided several times, the last set value will be used.

Example:
.nf
 curl --retry-delay 5 --retry https://example.com
.fi

See also \fI--retry\fP.
.IP "\-\-retry-max-time <seconds>"
The retry timer is reset before the first transfer attempt. Retries will be
done as usual (see \fI\-\-retry\fP) as long as the timer has not reached this given
limit. Notice that if the timer has not reached the limit, the request will be
made and while performing, it may take longer than this given time period. To
limit a single request's maximum time, use \fI\-m, \-\-max-time\fP. Set this option to
zero to not timeout retries.

If --retry-max-time is provided several times, the last set value will be used.

Example:
.nf
 curl --retry-max-time 30 --retry 10 https://example.com
.fi

See also \fI--retry\fP.
.IP "\-\-retry <num>"
If a transient error is returned when curl tries to perform a transfer, it
will retry this number of times before giving up. Setting the number to 0
makes curl do no retries (which is the default). Transient error means either:
a timeout, an FTP 4xx response code or an HTTP 408, 429, 500, 502, 503 or 504
response code.

When curl is about to retry a transfer, it will first wait one second and then
for all forthcoming retries it will double the waiting time until it reaches
10 minutes which then will be the delay between the rest of the retries. By
using \-\-retry-delay you disable this exponential backoff algorithm. See also
\-\-retry-max-time to limit the total time allowed for retries.

Since curl 7.66.0, curl will comply with the Retry-After: response header if
one was present to know when to issue the next retry.

If --retry is provided several times, the last set value will be used.

Example:
.nf
 curl --retry 7 https://example.com
.fi

See also \fI--retry-max-time\fP.
.IP "\-\-sasl-authzid <identity>"
Use this authorization identity (authzid), during SASL PLAIN authentication,
in addition to the authentication identity (authcid) as specified by \fI\-u, \-\-user\fP.

If the option is not specified, the server will derive the authzid from the
authcid, but if specified, and depending on the server implementation, it may
be used to access another user's inbox, that the user has been granted access
to, or a shared mailbox for example.

If --sasl-authzid is provided several times, the last set value will be used.

Example:
.nf
 curl --sasl-authzid zid imap://example.com/
.fi

See also \fI--login-options\fP. Added in 7.66.0.
.IP "\-\-sasl-ir"
Enable initial response in SASL authentication.

Providing --sasl-ir multiple times has no extra effect.
Disable it again with --no-sasl-ir.

Example:
.nf
 curl --sasl-ir imap://example.com/
.fi

See also \fI--sasl-authzid\fP. Added in 7.31.0.
.IP "\-\-service-name <name>"
This option allows you to change the service name for SPNEGO.

Examples: \fI\-\-negotiate\fP \-\-service-name sockd would use sockd/server-name.

If --service-name is provided several times, the last set value will be used.

Example:
.nf
 curl --service-name sockd/server https://example.com
.fi

See also \fI--negotiate\fP and \fI--proxy-service-name\fP. Added in 7.43.0.
.IP "\-S, \-\-show-error"
When used with \fI\-s, \-\-silent\fP, it makes curl show an error message if it fails.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Providing --show-error multiple times has no extra effect.
Disable it again with --no-show-error.

Example:
.nf
 curl --show-error --silent https://example.com
.fi

See also \fI--no-progress-meter\fP.
.IP "\-s, \-\-silent"
Silent or quiet mode. Do not show progress meter or error messages. Makes Curl
mute. It will still output the data you ask for, potentially even to the
terminal/stdout unless you redirect it.

Use \-\-show-error in addition to this option to disable progress meter but
still show error messages.

Providing --silent multiple times has no extra effect.
Disable it again with --no-silent.

Example:
.nf
 curl -s https://example.com
.fi

See also \fI-v, --verbose\fP, \fI--stderr\fP and \fI--no-progress-meter\fP.
.IP "\-\-socks4 <host[:port]>"
Use the specified SOCKS4 proxy. If the port number is not specified, it is
assumed at port 1080. Using this socket type make curl resolve the host name
and passing the address on to the proxy.

To specify proxy on a unix domain socket, use localhost for host, e.g.
socks4://localhost/path/to/socket.sock

This option overrides any previous use of \fI\-x, \-\-proxy\fP, as they are mutually
exclusive.

This option is superfluous since you can specify a socks4 proxy with \-\-proxy
using a socks4:// protocol prefix.

Since 7.52.0, \-\-preproxy can be used to specify a SOCKS proxy at the same time
\-\-proxy is used with an HTTP/HTTPS proxy. In such a case curl first connects to
the SOCKS proxy and then connects (through SOCKS) to the HTTP or HTTPS proxy.

If --socks4 is provided several times, the last set value will be used.

Example:
.nf
 curl --socks4 hostname:4096 https://example.com
.fi

See also \fI--socks4a\fP, \fI--socks5\fP and \fI--socks5-hostname\fP.
.IP "\-\-socks4a <host[:port]>"
Use the specified SOCKS4a proxy. If the port number is not specified, it is
assumed at port 1080. This asks the proxy to resolve the host name.

To specify proxy on a unix domain socket, use localhost for host, e.g.
socks4a://localhost/path/to/socket.sock

This option overrides any previous use of \fI\-x, \-\-proxy\fP, as they are mutually
exclusive.

This option is superfluous since you can specify a socks4a proxy with \-\-proxy
using a socks4a:// protocol prefix.

Since 7.52.0, \-\-preproxy can be used to specify a SOCKS proxy at the same time
\-\-proxy is used with an HTTP/HTTPS proxy. In such a case curl first connects to
the SOCKS proxy and then connects (through SOCKS) to the HTTP or HTTPS proxy.

If --socks4a is provided several times, the last set value will be used.

Example:
.nf
 curl --socks4a hostname:4096 https://example.com
.fi

See also \fI--socks4\fP, \fI--socks5\fP and \fI--socks5-hostname\fP.
.IP "\-\-socks5-basic"
Tells curl to use username/password authentication when connecting to a SOCKS5
proxy.  The username/password authentication is enabled by default.  Use
\-\-socks5-gssapi to force GSS-API authentication to SOCKS5 proxies.

Providing --socks5-basic multiple times has no extra effect.

Example:
.nf
 curl --socks5-basic --socks5 hostname:4096 https://example.com
.fi

See also \fI--socks5\fP. Added in 7.55.0.
.IP "\-\-socks5-gssapi-nec"
As part of the GSS-API negotiation a protection mode is negotiated. RFC 1961
says in section 4.3/4.4 it should be protected, but the NEC reference
implementation does not. The option \-\-socks5-gssapi-nec allows the
unprotected exchange of the protection mode negotiation.

Providing --socks5-gssapi-nec multiple times has no extra effect.
Disable it again with --no-socks5-gssapi-nec.

Example:
.nf
 curl --socks5-gssapi-nec --socks5 hostname:4096 https://example.com
.fi

See also \fI--socks5\fP.
.IP "\-\-socks5-gssapi-service <name>"
The default service name for a socks server is rcmd/server-fqdn. This option
allows you to change it.

Examples: \-\-socks5 proxy-name \-\-socks5-gssapi-service sockd would use
sockd/proxy-name \-\-socks5 proxy-name \-\-socks5-gssapi-service sockd/real-name
would use sockd/real-name for cases where the proxy-name does not match the
principal name.

If --socks5-gssapi-service is provided several times, the last set value will be used.

Example:
.nf
 curl --socks5-gssapi-service sockd --socks5 hostname:4096 https://example.com
.fi

See also \fI--socks5\fP.
.IP "\-\-socks5-gssapi"
Tells curl to use GSS-API authentication when connecting to a SOCKS5 proxy.
The GSS-API authentication is enabled by default (if curl is compiled with
GSS-API support).  Use \-\-socks5-basic to force username/password authentication
to SOCKS5 proxies.

Providing --socks5-gssapi multiple times has no extra effect.
Disable it again with --no-socks5-gssapi.

Example:
.nf
 curl --socks5-gssapi --socks5 hostname:4096 https://example.com
.fi

See also \fI--socks5\fP. Added in 7.55.0.
.IP "\-\-socks5-hostname <host[:port]>"
Use the specified SOCKS5 proxy (and let the proxy resolve the host name). If
the port number is not specified, it is assumed at port 1080.

To specify proxy on a unix domain socket, use localhost for host, e.g.
socks5h://localhost/path/to/socket.sock

This option overrides any previous use of \fI\-x, \-\-proxy\fP, as they are mutually
exclusive.

This option is superfluous since you can specify a socks5 hostname proxy with
\-\-proxy using a socks5h:// protocol prefix.

Since 7.52.0, \-\-preproxy can be used to specify a SOCKS proxy at the same time
\-\-proxy is used with an HTTP/HTTPS proxy. In such a case curl first connects to
the SOCKS proxy and then connects (through SOCKS) to the HTTP or HTTPS proxy.

If --socks5-hostname is provided several times, the last set value will be used.

Example:
.nf
 curl --socks5-hostname proxy.example:7000 https://example.com
.fi

See also \fI--socks5\fP and \fI--socks4a\fP.
.IP "\-\-socks5 <host[:port]>"
Use the specified SOCKS5 proxy \- but resolve the host name locally. If the
port number is not specified, it is assumed at port 1080.

To specify proxy on a unix domain socket, use localhost for host, e.g.
socks5://localhost/path/to/socket.sock

This option overrides any previous use of \fI\-x, \-\-proxy\fP, as they are mutually
exclusive.

This option is superfluous since you can specify a socks5 proxy with \-\-proxy
using a socks5:// protocol prefix.

Since 7.52.0, \-\-preproxy can be used to specify a SOCKS proxy at the same time
\-\-proxy is used with an HTTP/HTTPS proxy. In such a case curl first connects to
the SOCKS proxy and then connects (through SOCKS) to the HTTP or HTTPS proxy.

This option (as well as \fI\-\-socks4\fP) does not work with IPV6, FTPS or LDAP.

If --socks5 is provided several times, the last set value will be used.

Example:
.nf
 curl --socks5 proxy.example:7000 https://example.com
.fi

See also \fI--socks5-hostname\fP and \fI--socks4a\fP.
.IP "\-Y, \-\-speed-limit <speed>"
If a transfer is slower than this given speed (in bytes per second) for
speed-time seconds it gets aborted. speed-time is set with \-\-speed-time and is
30 if not set.

If --speed-limit is provided several times, the last set value will be used.

Example:
.nf
 curl --speed-limit 300 --speed-time 10 https://example.com
.fi

See also \fI-y, --speed-time\fP, \fI--limit-rate\fP and \fI-m, --max-time\fP.
.IP "\-y, \-\-speed-time <seconds>"
If a transfer runs slower than speed-limit bytes per second during a speed-time
period, the transfer is aborted. If speed-time is used, the default
speed-limit will be 1 unless set with \fI\-Y, \-\-speed-limit\fP.

This option controls transfers (in both directions) but will not affect slow
connects etc. If this is a concern for you, try the \-\-connect-timeout option.

If --speed-time is provided several times, the last set value will be used.

Example:
.nf
 curl --speed-limit 300 --speed-time 10 https://example.com
.fi

See also \fI-Y, --speed-limit\fP and \fI--limit-rate\fP.
.IP "\-\-ssl-allow-beast"
This option tells curl to not work around a security flaw in the SSL3 and
TLS1.0 protocols known as BEAST.  If this option is not used, the SSL layer
may use workarounds known to cause interoperability problems with some older
SSL implementations.

\fBWARNING\fP: this option loosens the SSL security, and by using this flag you
ask for exactly that.

Providing --ssl-allow-beast multiple times has no extra effect.
Disable it again with --no-ssl-allow-beast.

Example:
.nf
 curl --ssl-allow-beast https://example.com
.fi

See also \fI--proxy-ssl-allow-beast\fP and \fI-k, --insecure\fP.
.IP "\-\-ssl-auto-client-cert"
Tell libcurl to automatically locate and use a client certificate for
authentication, when requested by the server. This option is only supported
for Schannel (the native Windows SSL library). Prior to 7.77.0 this was the
default behavior in libcurl with Schannel. Since the server can request any
certificate that supports client authentication in the OS certificate store it
could be a privacy violation and unexpected.

Providing --ssl-auto-client-cert multiple times has no extra effect.
Disable it again with --no-ssl-auto-client-cert.

Example:
.nf
 curl --ssl-auto-client-cert https://example.com
.fi

See also \fI--proxy-ssl-auto-client-cert\fP. Added in 7.77.0.
.IP "\-\-ssl-no-revoke"
(Schannel) This option tells curl to disable certificate revocation checks.
WARNING: this option loosens the SSL security, and by using this flag you ask
for exactly that.

Providing --ssl-no-revoke multiple times has no extra effect.
Disable it again with --no-ssl-no-revoke.

Example:
.nf
 curl --ssl-no-revoke https://example.com
.fi

See also \fI--crlfile\fP. Added in 7.44.0.
.IP "\-\-ssl-reqd"
(FTP IMAP POP3 SMTP LDAP) Require SSL/TLS for the connection. Terminates the connection if the server
does not support SSL/TLS.

This option is handled in LDAP since version 7.81.0. It is fully supported
by the OpenLDAP backend and rejected by the generic ldap backend if explicit
TLS is required.

This option was formerly known as \-\-ftp-ssl-reqd.

Providing --ssl-reqd multiple times has no extra effect.
Disable it again with --no-ssl-reqd.

Example:
.nf
 curl --ssl-reqd ftp://example.com
.fi

See also \fI--ssl\fP and \fI-k, --insecure\fP.
.IP "\-\-ssl-revoke-best-effort"
(Schannel) This option tells curl to ignore certificate revocation checks when
they failed due to missing/offline distribution points for the revocation check
lists.

Providing --ssl-revoke-best-effort multiple times has no extra effect.
Disable it again with --no-ssl-revoke-best-effort.

Example:
.nf
 curl --ssl-revoke-best-effort https://example.com
.fi

See also \fI--crlfile\fP and \fI-k, --insecure\fP. Added in 7.70.0.
.IP "\-\-ssl"
(FTP IMAP POP3 SMTP LDAP) Warning: this is considered an insecure option. Consider using \-\-ssl-reqd
instead to be sure curl upgrades to a secure connection.

Try to use SSL/TLS for the connection. Reverts to a non-secure connection if
the server does not support SSL/TLS. See also \-\-ftp-ssl-control and \-\-ssl-reqd
for different levels of encryption required.

This option is handled in LDAP since version 7.81.0. It is fully supported
by the OpenLDAP backend and ignored by the generic ldap backend.

Please note that a server may close the connection if the negotiation does
not succeed.

This option was formerly known as \-\-ftp-ssl. That option
name can still be used but will be removed in a future version.

Providing --ssl multiple times has no extra effect.
Disable it again with --no-ssl.

Example:
.nf
 curl --ssl pop3://example.com/
.fi

See also \fI--ssl-reqd\fP, \fI-k, --insecure\fP and \fI--ciphers\fP.
.IP "\-2, \-\-sslv2"
(SSL) This option previously asked curl to use SSLv2, but starting in curl 7.77.0
this instruction is ignored. SSLv2 is widely considered insecure (see RFC
6176).

Providing --sslv2 multiple times has no extra effect.

Example:
.nf
 curl --sslv2 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http2\fP. \fI-2, --sslv2\fP requires that the underlying libcurl was built to support TLS. This option is mutually exclusive to \fI-3, --sslv3\fP and \fI-1, --tlsv1\fP and \fI--tlsv1.1\fP and \fI--tlsv1.2\fP.
.IP "\-3, \-\-sslv3"
(SSL) This option previously asked curl to use SSLv3, but starting in curl 7.77.0
this instruction is ignored. SSLv3 is widely considered insecure (see RFC
7568).

Providing --sslv3 multiple times has no extra effect.

Example:
.nf
 curl --sslv3 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http2\fP. \fI-3, --sslv3\fP requires that the underlying libcurl was built to support TLS. This option is mutually exclusive to \fI-2, --sslv2\fP and \fI-1, --tlsv1\fP and \fI--tlsv1.1\fP and \fI--tlsv1.2\fP.
.IP "\-\-stderr <file>"
Redirect all writes to stderr to the specified file instead. If the file name
is a plain '-', it is instead written to stdout.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

If --stderr is provided several times, the last set value will be used.

Example:
.nf
 curl --stderr output.txt https://example.com
.fi

See also \fI-v, --verbose\fP and \fI-s, --silent\fP.
.IP "\-\-styled-output"
Enables the automatic use of bold font styles when writing HTTP headers to the
terminal. Use \-\-no-styled-output to switch them off.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Providing --styled-output multiple times has no extra effect.
Disable it again with --no-styled-output.

Example:
.nf
 curl --styled-output -I https://example.com
.fi

See also \fI-I, --head\fP and \fI-v, --verbose\fP. Added in 7.61.0.
.IP "\-\-suppress-connect-headers"
When \-\-proxytunnel is used and a CONNECT request is made do not output proxy
CONNECT response headers. This option is meant to be used with \-\-dump-header or
\-\-include which are used to show protocol headers in the output. It has no
effect on debug options such as \-\-verbose or \fI\-\-trace\fP, or any statistics.

Providing --suppress-connect-headers multiple times has no extra effect.
Disable it again with --no-suppress-connect-headers.

Example:
.nf
 curl --suppress-connect-headers --include -x proxy https://example.com
.fi

See also \fI-D, --dump-header\fP, \fI-i, --include\fP and \fI-p, --proxytunnel\fP. Added in 7.54.0.
.IP "\-\-tcp-fastopen"
Enable use of TCP Fast Open (RFC7413).

Providing --tcp-fastopen multiple times has no extra effect.
Disable it again with --no-tcp-fastopen.

Example:
.nf
 curl --tcp-fastopen https://example.com
.fi

See also \fI--false-start\fP. Added in 7.49.0.
.IP "\-\-tcp-nodelay"
Turn on the TCP_NODELAY option. See the \fIcurl_easy_setopt(3)\fP man page for
details about this option.

Since 7.50.2, curl sets this option by default and you need to explicitly
switch it off if you do not want it on.

Providing --tcp-nodelay multiple times has no extra effect.
Disable it again with --no-tcp-nodelay.

Example:
.nf
 curl --tcp-nodelay https://example.com
.fi

See also \fI-N, --no-buffer\fP.
.IP "\-t, \-\-telnet-option <opt=val>"
Pass options to the telnet protocol. Supported options are:

TTYPE=<term> Sets the terminal type.

XDISPLOC=<X display> Sets the X display location.

NEW_ENV=<var,val> Sets an environment variable.

--telnet-option can be used several times in a command line

Example:
.nf
 curl -t TTYPE=vt100 telnet://example.com/
.fi

See also \fI-K, --config\fP.
.IP "\-\-tftp-blksize <value>"
(TFTP) Set TFTP BLKSIZE option (must be >512). This is the block size that curl will
try to use when transferring data to or from a TFTP server. By default 512
bytes will be used.

If --tftp-blksize is provided several times, the last set value will be used.

Example:
.nf
 curl --tftp-blksize 1024 tftp://example.com/file
.fi

See also \fI--tftp-no-options\fP.
.IP "\-\-tftp-no-options"
(TFTP) Tells curl not to send TFTP options requests.

This option improves interop with some legacy servers that do not acknowledge
or properly implement TFTP options. When this option is used \-\-tftp-blksize is
ignored.

Providing --tftp-no-options multiple times has no extra effect.
Disable it again with --no-tftp-no-options.

Example:
.nf
 curl --tftp-no-options tftp://***********/
.fi

See also \fI--tftp-blksize\fP. Added in 7.48.0.
.IP "\-z, \-\-time-cond <time>"
(HTTP FTP) Request a file that has been modified later than the given time and date, or
one that has been modified before that time. The <date expression> can be all
sorts of date strings or if it does not match any internal ones, it is taken as
a filename and tries to get the modification date (mtime) from <file>
instead. See the \fIcurl_getdate(3)\fP man pages for date expression details.

Start the date expression with a dash (-) to make it request for a document
that is older than the given date/time, default is a document that is newer
than the specified date/time.

If --time-cond is provided several times, the last set value will be used.

Examples:
.nf
 curl -z "Wed 01 Sep 2021 12:18:00" https://example.com
 curl -z "-Wed 01 Sep 2021 12:18:00" https://example.com
 curl -z file https://example.com
.fi

See also \fI--etag-compare\fP and \fI-R, --remote-time\fP.
.IP "\-\-tls-max <VERSION>"
(SSL) VERSION defines maximum supported TLS version. The minimum acceptable version
is set by tlsv1.0, tlsv1.1, tlsv1.2 or tlsv1.3.

If the connection is done without TLS, this option has no effect. This
includes QUIC-using (HTTP/3) transfers.

.RS
.IP "default"
Use up to recommended TLS version.
.IP "1.0"
Use up to TLSv1.0.
.IP "1.1"
Use up to TLSv1.1.
.IP "1.2"
Use up to TLSv1.2.
.IP "1.3"
Use up to TLSv1.3.
.RE

If --tls-max is provided several times, the last set value will be used.

Examples:
.nf
 curl --tls-max 1.2 https://example.com
 curl --tls-max 1.3 --tlsv1.2 https://example.com
.fi

See also \fI--tlsv1.0\fP, \fI--tlsv1.1\fP, \fI--tlsv1.2\fP and \fI--tlsv1.3\fP. \fI--tls-max\fP requires that the underlying libcurl was built to support TLS. Added in 7.54.0.
.IP "\-\-tls13-ciphers <ciphersuite list>"
(TLS) Specifies which cipher suites to use in the connection if it negotiates TLS
1.3. The list of ciphers suites must specify valid ciphers. Read up on TLS 1.3
cipher suite details on this URL:

.nf
 https://curl.se/docs/ssl-ciphers.html
.fi

This option is currently used only when curl is built to use OpenSSL 1.1.1 or
later. If you are using a different SSL backend you can try setting TLS 1.3
cipher suites by using the \-\-ciphers option.

If --tls13-ciphers is provided several times, the last set value will be used.

Example:
.nf
 curl --tls13-ciphers TLS_AES_128_GCM_SHA256 https://example.com
.fi

See also \fI--ciphers\fP and \fI--curves\fP. Added in 7.61.0.
.IP "\-\-tlsauthtype <type>"
Set TLS authentication type. Currently, the only supported option is "SRP",
for TLS-SRP (RFC 5054). If \-\-tlsuser and \-\-tlspassword are specified but
\-\-tlsauthtype is not, then this option defaults to "SRP". This option works
only if the underlying libcurl is built with TLS-SRP support, which requires
OpenSSL or GnuTLS with TLS-SRP support.

If --tlsauthtype is provided several times, the last set value will be used.

Example:
.nf
 curl --tlsauthtype SRP https://example.com
.fi

See also \fI--tlsuser\fP.
.IP "\-\-tlspassword <string>"
Set password for use with the TLS authentication method specified with
\fI\-\-tlsauthtype\fP. Requires that \-\-tlsuser also be set.

This option does not work with TLS 1.3.

If --tlspassword is provided several times, the last set value will be used.

Example:
.nf
 curl --tlspassword pwd --tlsuser user https://example.com
.fi

See also \fI--tlsuser\fP.
.IP "\-\-tlsuser <name>"
Set username for use with the TLS authentication method specified with
\fI\-\-tlsauthtype\fP. Requires that \-\-tlspassword also is set.

This option does not work with TLS 1.3.

If --tlsuser is provided several times, the last set value will be used.

Example:
.nf
 curl --tlspassword pwd --tlsuser user https://example.com
.fi

See also \fI--tlspassword\fP.
.IP "\-\-tlsv1.0"
(TLS) Forces curl to use TLS version 1.0 or later when connecting to a remote TLS server.

In old versions of curl this option was documented to allow _only_ TLS 1.0.
That behavior was inconsistent depending on the TLS library. Use \-\-tls-max if
you want to set a maximum TLS version.

Providing --tlsv1.0 multiple times has no extra effect.

Example:
.nf
 curl --tlsv1.0 https://example.com
.fi

See also \fI--tlsv1.3\fP. Added in 7.34.0.
.IP "\-\-tlsv1.1"
(TLS) Forces curl to use TLS version 1.1 or later when connecting to a remote TLS server.

In old versions of curl this option was documented to allow _only_ TLS 1.1.
That behavior was inconsistent depending on the TLS library. Use \-\-tls-max if
you want to set a maximum TLS version.

Providing --tlsv1.1 multiple times has no extra effect.

Example:
.nf
 curl --tlsv1.1 https://example.com
.fi

See also \fI--tlsv1.3\fP and \fI--tls-max\fP. Added in 7.34.0.
.IP "\-\-tlsv1.2"
(TLS) Forces curl to use TLS version 1.2 or later when connecting to a remote TLS server.

In old versions of curl this option was documented to allow _only_ TLS 1.2.
That behavior was inconsistent depending on the TLS library. Use \-\-tls-max if
you want to set a maximum TLS version.

Providing --tlsv1.2 multiple times has no extra effect.

Example:
.nf
 curl --tlsv1.2 https://example.com
.fi

See also \fI--tlsv1.3\fP and \fI--tls-max\fP. Added in 7.34.0.
.IP "\-\-tlsv1.3"
(TLS) Forces curl to use TLS version 1.3 or later when connecting to a remote TLS
server.

If the connection is done without TLS, this option has no effect. This
includes QUIC-using (HTTP/3) transfers.

Note that TLS 1.3 is not supported by all TLS backends.

Providing --tlsv1.3 multiple times has no extra effect.

Example:
.nf
 curl --tlsv1.3 https://example.com
.fi

See also \fI--tlsv1.2\fP and \fI--tls-max\fP. Added in 7.52.0.
.IP "\-1, \-\-tlsv1"
(SSL) Tells curl to use at least TLS version 1.x when negotiating with a remote TLS
server. That means TLS version 1.0 or higher

Providing --tlsv1 multiple times has no extra effect.

Example:
.nf
 curl --tlsv1 https://example.com
.fi

See also \fI--http1.1\fP and \fI--http2\fP. \fI-1, --tlsv1\fP requires that the underlying libcurl was built to support TLS. This option is mutually exclusive to \fI--tlsv1.1\fP and \fI--tlsv1.2\fP and \fI--tlsv1.3\fP.
.IP "\-\-tr-encoding"
(HTTP) Request a compressed Transfer-Encoding response using one of the algorithms
curl supports, and uncompress the data while receiving it.

Providing --tr-encoding multiple times has no extra effect.
Disable it again with --no-tr-encoding.

Example:
.nf
 curl --tr-encoding https://example.com
.fi

See also \fI--compressed\fP.
.IP "\-\-trace-ascii <file>"
Enables a full trace dump of all incoming and outgoing data, including
descriptive information, to the given output file. Use "-" as filename to have
the output sent to stdout.

This is similar to \fI\-\-trace\fP, but leaves out the hex part and only shows the
ASCII part of the dump. It makes smaller output that might be easier to read
for untrained humans.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

If --trace-ascii is provided several times, the last set value will be used.

Example:
.nf
 curl --trace-ascii log.txt https://example.com
.fi

See also \fI-v, --verbose\fP and \fI--trace\fP. This option is mutually exclusive to \fI--trace\fP and \fI-v, --verbose\fP.
.IP "\-\-trace-time"
Prepends a time stamp to each trace or verbose line that curl displays.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Providing --trace-time multiple times has no extra effect.
Disable it again with --no-trace-time.

Example:
.nf
 curl --trace-time --trace-ascii output https://example.com
.fi

See also \fI--trace\fP and \fI-v, --verbose\fP.
.IP "\-\-trace <file>"
Enables a full trace dump of all incoming and outgoing data, including
descriptive information, to the given output file. Use "-" as filename to have
the output sent to stdout. Use "%" as filename to have the output sent to
stderr.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

If --trace is provided several times, the last set value will be used.

Example:
.nf
 curl --trace log.txt https://example.com
.fi

See also \fI--trace-ascii\fP and \fI--trace-time\fP. This option is mutually exclusive to \fI-v, --verbose\fP and \fI--trace-ascii\fP.
.IP "\-\-unix-socket <path>"
(HTTP) Connect through this Unix domain socket, instead of using the network.

If --unix-socket is provided several times, the last set value will be used.

Example:
.nf
 curl --unix-socket socket-path https://example.com
.fi

See also \fI--abstract-unix-socket\fP. Added in 7.40.0.
.IP "\-T, \-\-upload-file <file>"
This transfers the specified local file to the remote URL. If there is no file
part in the specified URL, curl will append the local file name. NOTE that you
must use a trailing / on the last directory to really prove to Curl that there
is no file name or curl will think that your last directory name is the remote
file name to use. That will most likely cause the upload operation to fail. If
this is used on an HTTP(S) server, the PUT command will be used.

Use the file name "-" (a single dash) to use stdin instead of a given file.
Alternately, the file name "." (a single period) may be specified instead of
\(dq-" to use stdin in non-blocking mode to allow reading server output while
stdin is being uploaded.

You can specify one \-\-upload-file for each URL on the command line. Each
\fI\-T, \-\-upload-file\fP + URL pair specifies what to upload and to where. curl also
supports "globbing" of the \-\-upload-file argument, meaning that you can upload
multiple files to a single URL by using the same URL globbing style supported
in the URL.

When uploading to an SMTP server: the uploaded data is assumed to be RFC 5322
formatted. It has to feature the necessary set of headers and mail body
formatted correctly by the user as curl will not transcode nor encode it
further in any way.

--upload-file can be used several times in a command line

Examples:
.nf
 curl -T file https://example.com
 curl -T "img[1-1000].png" ftp://ftp.example.com/
 curl --upload-file "{file1,file2}" https://example.com
.fi

See also \fI-G, --get\fP and \fI-I, --head\fP.
.IP "\-\-url <url>"
Specify a URL to fetch. This option is mostly handy when you want to specify
URL(s) in a config file.

If the given URL is missing a scheme name (such as "http://" or "ftp://" etc)
then curl will make a guess based on the host. If the outermost sub-domain
name matches DICT, FTP, IMAP, LDAP, POP3 or SMTP then that protocol will be
used, otherwise HTTP will be used. Since 7.45.0 guessing can be disabled by
setting a default protocol, see \-\-proto-default for details.

To control where this URL is written, use the \-\-output or the \-\-remote-name
options.

\fBWARNING\fP: On Windows, particular file:// accesses can be converted to
network accesses by the operating system. Beware!

--url can be used several times in a command line

Example:
.nf
 curl --url https://example.com
.fi

See also \fI-:, --next\fP and \fI-K, --config\fP.
.IP "\-B, \-\-use-ascii"
(FTP LDAP) Enable ASCII transfer. For FTP, this can also be enforced by using a URL that
ends with ";type=A". This option causes data sent to stdout to be in text mode
for win32 systems.

Providing --use-ascii multiple times has no extra effect.
Disable it again with --no-use-ascii.

Example:
.nf
 curl -B ftp://example.com/README
.fi

See also \fI--crlf\fP and \fI--data-ascii\fP.
.IP "\-A, \-\-user-agent <name>"
(HTTP) Specify the User-Agent string to send to the HTTP server. To encode blanks in
the string, surround the string with single quote marks. This header can also
be set with the \-\-header or the \-\-proxy-header options.

If you give an empty argument to \fI\-A, \-\-user-agent\fP (""), it will remove the header
completely from the request. If you prefer a blank header, you can set it to a
single space (" ").

If --user-agent is provided several times, the last set value will be used.

Example:
.nf
 curl -A "Agent 007" https://example.com
.fi

See also \fI-H, --header\fP and \fI--proxy-header\fP.
.IP "\-u, \-\-user <user:password>"
Specify the user name and password to use for server authentication. Overrides
\-\-netrc and \fI\-\-netrc-optional\fP.

If you simply specify the user name, curl will prompt for a password.

The user name and passwords are split up on the first colon, which makes it
impossible to use a colon in the user name with this option. The password can,
still.

On systems where it works, curl will hide the given option argument from
process listings. This is not enough to protect credentials from possibly
getting seen by other users on the same system as they will still be visible
for a moment before cleared. Such sensitive data should be retrieved from a
file instead or similar and never used in clear text in a command line.

When using Kerberos V5 with a Windows based server you should include the
Windows domain name in the user name, in order for the server to successfully
obtain a Kerberos Ticket. If you do not, then the initial authentication
handshake may fail.

When using NTLM, the user name can be specified simply as the user name,
without the domain, if there is a single domain and forest in your setup
for example.

To specify the domain name use either Down-Level Logon Name or UPN (User
Principal Name) formats. For example, EXAMPLE\\<NAME_EMAIL>
respectively.

If you use a Windows SSPI-enabled curl binary and perform Kerberos V5,
Negotiate, NTLM or Digest authentication then you can tell curl to select
the user name and password from your environment by specifying a single colon
with this option: "-u :".

If --user is provided several times, the last set value will be used.

Example:
.nf
 curl -u user:secret https://example.com
.fi

See also \fI-n, --netrc\fP and \fI-K, --config\fP.
.IP "\-v, \-\-verbose"
Makes curl verbose during the operation. Useful for debugging and seeing
what's going on "under the hood". A line starting with '>' means "header data"
sent by curl, '<' means "header data" received by curl that is hidden in
normal cases, and a line starting with '*' means additional info provided by
curl.

If you only want HTTP headers in the output, \-\-include might be the option
you are looking for.

If you think this option still does not give you enough details, consider using
\-\-trace or \-\-trace-ascii instead.

This option is global and does not need to be specified for each use of
\fI\-:, \-\-next\fP.

Use \-\-silent to make curl really quiet.

Providing --verbose multiple times has no extra effect.
Disable it again with --no-verbose.

Example:
.nf
 curl --verbose https://example.com
.fi

See also \fI-i, --include\fP. This option is mutually exclusive to \fI--trace\fP and \fI--trace-ascii\fP.
.IP "\-V, \-\-version"
Displays information about curl and the libcurl version it uses.

The first line includes the full version of curl, libcurl and other 3rd party
libraries linked with the executable.

The second line (starts with "Protocols:") shows all protocols that libcurl
reports to support.

The third line (starts with "Features:") shows specific features libcurl
reports to offer. Available features include:
.RS
.IP "alt-svc"
Support for the Alt-Svc: header is provided.
.IP "AsynchDNS"
This curl uses asynchronous name resolves. Asynchronous name resolves can be
done using either the c-ares or the threaded resolver backends.
.IP "brotli"
Support for automatic brotli compression over HTTP(S).
.IP "CharConv"
curl was built with support for character set conversions (like EBCDIC)
.IP "Debug"
This curl uses a libcurl built with Debug. This enables more error-tracking
and memory debugging etc. For curl-developers only!
.IP "gsasl"
The built-in SASL authentication includes extensions to support SCRAM because
libcurl was built with libgsasl.
.IP "GSS-API"
GSS-API is supported.
.IP "HSTS"
HSTS support is present.
.IP "HTTP2"
HTTP/2 support has been built-in.
.IP "HTTP3"
HTTP/3 support has been built-in.
.IP "HTTPS-proxy"
This curl is built to support HTTPS proxy.
.IP "IDN"
This curl supports IDN \- international domain names.
.IP "IPv6"
You can use IPv6 with this.
.IP "Kerberos"
Kerberos V5 authentication is supported.
.IP "Largefile"
This curl supports transfers of large files, files larger than 2GB.
.IP "libz"
Automatic decompression (via gzip, deflate) of compressed files over HTTP is
supported.
.IP "MultiSSL"
This curl supports multiple TLS backends.
.IP "NTLM"
NTLM authentication is supported.
.IP "NTLM_WB"
NTLM delegation to winbind helper is supported.
.IP "PSL"
PSL is short for Public Suffix List and means that this curl has been built
with knowledge about "public suffixes".
.IP "SPNEGO"
SPNEGO authentication is supported.
.IP "SSL"
SSL versions of various protocols are supported, such as HTTPS, FTPS, POP3S
and so on.
.IP "SSPI"
SSPI is supported.
.IP "TLS-SRP"
SRP (Secure Remote Password) authentication is supported for TLS.
.IP "TrackMemory"
Debug memory tracking is supported.
.IP "Unicode"
Unicode support on Windows.
.IP "UnixSockets"
Unix sockets support is provided.
.IP "zstd"
Automatic decompression (via zstd) of compressed files over HTTP is supported.
.RE

Providing --version multiple times has no extra effect.
Disable it again with --no-version.

Example:
.nf
 curl --version
.fi

See also \fI-h, --help\fP and \fI-M, --manual\fP.
.IP "\-w, \-\-write-out <format>"
Make curl display information on stdout after a completed transfer. The format
is a string that may contain plain text mixed with any number of
variables. The format can be specified as a literal "string", or you can have
curl read the format from a file with "@filename" and to tell curl to read the
format from stdin you write "@-".

The variables present in the output format will be substituted by the value or
text that curl thinks fit, as described below. All variables are specified as
%{variable_name} and to output a normal % you just write them as %%. You can
output a newline by using \\n, a carriage return with \\r and a tab space with
\\t.

The output will be written to standard output, but this can be switched to
standard error by using %{stderr}.

Output HTTP headers from the most recent request by using \fB%header{name}\fP
where \fBname\fP is the case insensitive name of the header (without the
trailing colon). The header contents are exactly as sent over the network,
with leading and trailing whitespace trimmed. Added in curl 7.84.0.

.B NOTE:
The %-symbol is a special symbol in the win32-environment, where all
occurrences of % must be doubled when using this option.

The variables available are:
.RS
.TP 15
.B content_type
The Content-Type of the requested document, if there was any.
.TP
.B errormsg
The error message. (Added in 7.75.0)
.TP
.B exitcode
The numerical exitcode of the transfer. (Added in 7.75.0)
.TP
.B filename_effective
The ultimate filename that curl writes out to. This is only meaningful if curl
is told to write to a file with the \-\-remote-name or \-\-output
option. It's most useful in combination with the \-\-remote-header-name
option.
.TP
.B ftp_entry_path
The initial path curl ended up in when logging on to the remote FTP
server.
.TP
.B header_json
A JSON object with all HTTP response headers from the recent transfer. Values
are provided as arrays, since in the case of multiple headers there can be
multiple values.

The header names provided in lowercase, listed in order of appearance over the
wire. Except for duplicated headers. They are grouped on the first occurrence
of that header, each value is presented in the JSON array.
.TP
.B http_code
The numerical response code that was found in the last retrieved HTTP(S) or
FTP(s) transfer.
.TP
.B http_connect
The numerical code that was found in the last response (from a proxy) to a
curl CONNECT request.
.TP
.B http_version
The http version that was effectively used. (Added in 7.50.0)
.TP
.B json
A JSON object with all available keys.
.TP
.B local_ip
The IP address of the local end of the most recently done connection \- can be
either IPv4 or IPv6.
.TP
.B local_port
The local port number of the most recently done connection.
.TP
.B method
The http method used in the most recent HTTP request. (Added in 7.72.0)
.TP
.B num_connects
Number of new connects made in the recent transfer.
.TP
.B num_headers
The number of response headers in the most recent request (restarted at each
redirect). Note that the status line IS NOT a header. (Added in 7.73.0)
.TP
.B num_redirects
Number of redirects that were followed in the request.
.TP
.B onerror
The rest of the output is only shown if the transfer returned a non-zero error
(Added in 7.75.0)
.TP
.B proxy_ssl_verify_result
The result of the HTTPS proxy's SSL peer certificate verification that was
requested. 0 means the verification was successful. (Added in 7.52.0)
.TP
.B redirect_url
When an HTTP request was made without \-\-location to follow redirects (or when
\-\-max-redirs is met), this variable will show the actual URL a redirect
\fIwould\fP have gone to.
.TP
.B referer
The Referer: header, if there was any. (Added in 7.76.0)
.TP
.B remote_ip
The remote IP address of the most recently done connection \- can be either
IPv4 or IPv6.
.TP
.B remote_port
The remote port number of the most recently done connection.
.TP
.B response_code
The numerical response code that was found in the last transfer (formerly
known as "http_code").
.TP
.B scheme
The URL scheme (sometimes called protocol) that was effectively used. (Added in 7.52.0)
.TP
.B size_download
The total amount of bytes that were downloaded. This is the size of the
body/data that was transferred, excluding headers.
.TP
.B size_header
The total amount of bytes of the downloaded headers.
.TP
.B size_request
The total amount of bytes that were sent in the HTTP request.
.TP
.B size_upload
The total amount of bytes that were uploaded. This is the size of the
body/data that was transferred, excluding headers.
.TP
.B speed_download
The average download speed that curl measured for the complete download. Bytes
per second.
.TP
.B speed_upload
The average upload speed that curl measured for the complete upload. Bytes per
second.
.TP
.B ssl_verify_result
The result of the SSL peer certificate verification that was requested. 0
means the verification was successful.
.TP
.B stderr
From this point on, the \-\-write-out output will be written to standard
error. (Added in 7.63.0)
.TP
.B stdout
From this point on, the \-\-write-out output will be written to standard output.
This is the default, but can be used to switch back after switching to stderr.
(Added in 7.63.0)
.TP
.B time_appconnect
The time, in seconds, it took from the start until the SSL/SSH/etc
connect/handshake to the remote host was completed.
.TP
.B time_connect
The time, in seconds, it took from the start until the TCP connect to the
remote host (or proxy) was completed.
.TP
.B time_namelookup
The time, in seconds, it took from the start until the name resolving was
completed.
.TP
.B time_pretransfer
The time, in seconds, it took from the start until the file transfer was just
about to begin. This includes all pre-transfer commands and negotiations that
are specific to the particular protocol(s) involved.
.TP
.B time_redirect
The time, in seconds, it took for all redirection steps including name lookup,
connect, pretransfer and transfer before the final transaction was
started. time_redirect shows the complete execution time for multiple
redirections.
.TP
.B time_starttransfer
The time, in seconds, it took from the start until the first byte was just
about to be transferred. This includes time_pretransfer and also the time the
server needed to calculate the result.
.TP
.B time_total
The total time, in seconds, that the full operation lasted.
.TP
.B url
The URL that was fetched. (Added in 7.75.0)
.TP
.B urlnum
The URL index number of this transfer, 0-indexed. De-globbed URLs share the
same index number as the origin globbed URL. (Added in 7.75.0)
.TP
.B url_effective
The URL that was fetched last. This is most meaningful if you have told curl
to follow location: headers.
.RE
.IP

If --write-out is provided several times, the last set value will be used.

Example:
.nf
 curl -w '%{http_code}\\n' https://example.com
.fi

See also \fI-v, --verbose\fP and \fI-I, --head\fP.
.IP "\-\-xattr"
When saving output to a file, this option tells curl to store certain file
metadata in extended file attributes. Currently, the URL is stored in the
xdg.origin.url attribute and, for HTTP, the content type is stored in
the mime_type attribute. If the file system does not support extended
attributes, a warning is issued.

Providing --xattr multiple times has no extra effect.
Disable it again with --no-xattr.

Example:
.nf
 curl --xattr -o storage https://example.com
.fi

See also \fI-R, --remote-time\fP, \fI-w, --write-out\fP and \fI-v, --verbose\fP.
.SH FILES
.I ~/.curlrc
.RS
Default config file, see \-\-config for details.
.SH ENVIRONMENT
The environment variables can be specified in lower case or upper case. The
lower case version has precedence. http_proxy is an exception as it is only
available in lower case.

Using an environment variable to set the proxy has the same effect as using
the \-\-proxy option.

.IP "http_proxy [protocol://]<host>[:port]"
Sets the proxy server to use for HTTP.
.IP "HTTPS_PROXY [protocol://]<host>[:port]"
Sets the proxy server to use for HTTPS.
.IP "[url-protocol]_PROXY [protocol://]<host>[:port]"
Sets the proxy server to use for [url-protocol], where the protocol is a
protocol that curl supports and as specified in a URL. FTP, FTPS, POP3, IMAP,
SMTP, LDAP, etc.
.IP "ALL_PROXY [protocol://]<host>[:port]"
Sets the proxy server to use if no protocol-specific proxy is set.
.IP "NO_PROXY <comma-separated list of hosts/domains>"
list of host names that should not go through any proxy. If set to an asterisk
\(aq*' only, it matches all hosts. Each name in this list is matched as either
a domain name which contains the hostname, or the hostname itself.

This environment variable disables use of the proxy even when specified with
the \-\-proxy option. That is
.B NO_PROXY=direct.example.com curl \-x http://proxy.example.com
.B http://direct.example.com
accesses the target URL directly, and
.B NO_PROXY=direct.example.com curl \-x http://proxy.example.com
.B http://somewhere.example.com
accesses the target URL through the proxy.

The list of host names can also be include numerical IP addresses, and IPv6
versions should then be given without enclosing brackets.

IPv6 numerical addresses are compared as strings, so they will only match if
the representations are the same: "::1" is the same as "::0:1" but they do not
match.
.IP "APPDATA <dir>"
On Windows, this variable is used when trying to find the home directory. If
the primary home variable are all unset.
.IP "COLUMNS <terminal width>"
If set, the specified number of characters will be used as the terminal width
when the alternative progress-bar is shown. If not set, curl will try to
figure it out using other ways.
.IP "CURL_CA_BUNDLE <file>"
If set, will be used as the \fI\-\-cacert\fP value.
.IP "CURL_HOME <dir>"
If set, is the first variable curl checks when trying to find its home
directory. If not set, it continues to check \fBXDG_CONFIG_HOME\fP.
.IP "CURL_SSL_BACKEND <TLS backend>"
If curl was built with support for "MultiSSL", meaning that it has built-in
support for more than one TLS backend, this environment variable can be set to
the case insensitive name of the particular backend to use when curl is
invoked. Setting a name that is not a built-in alternative will make curl
stay with the default.

SSL backend names (case-insensitive): bearssl, gnutls, gskit, mbedtls,
nss, openssl, rustls, schannel, secure-transport, wolfssl
.IP "HOME <dir>"
If set, this is used to find the home directory when that is needed. Like when
looking for the default .curlrc. \fBCURL_HOME\fP and \fBXDG_CONFIG_HOME\fP
have preference.
.IP "QLOGDIR <directory name>"
If curl was built with HTTP/3 support, setting this environment variable to a
local directory will make curl produce qlogs in that directory, using file
names named after the destination connection id (in hex). Do note that these
files can become rather large. Works with both QUIC backends.
.IP SHELL
Used on VMS when trying to detect if using a DCL or a "unix" shell.
.IP "SSL_CERT_DIR <dir>"
If set, will be used as the \fI\-\-capath\fP value.
.IP "SSL_CERT_FILE <path>"
If set, will be used as the \fI\-\-cacert\fP value.
.IP "SSLKEYLOGFILE <file name>"
If you set this environment variable to a file name, curl will store TLS
secrets from its connections in that file when invoked to enable you to
analyze the TLS traffic in real time using network analyzing tools such as
Wireshark. This works with the following TLS backends: OpenSSL, libressl,
BoringSSL, GnuTLS, NSS and wolfSSL.
.IP "USERPROFILE <dir>"
On Windows, this variable is used when trying to find the home directory. If
the other, primary, variable are all unset. If set, curl will use the path
\(dq$USERPROFILE\\Application Data".
.IP "XDG_CONFIG_HOME <dir>"
If \fBCURL_HOME\fP is not set, this variable is checked when looking for a
default .curlrc file.
.SH "PROXY PROTOCOL PREFIXES"
The proxy string may be specified with a protocol:// prefix to specify
alternative proxy protocols.

If no protocol is specified in the proxy string or if the string does not match
a supported one, the proxy will be treated as an HTTP proxy.

The supported proxy protocol prefixes are as follows:
.IP "http://"
Makes it use it as an HTTP proxy. The default if no scheme prefix is used.
.IP "https://"
Makes it treated as an \fBHTTPS\fP proxy.
.IP "socks4://"
Makes it the equivalent of \-\-socks4
.IP "socks4a://"
Makes it the equivalent of \-\-socks4a
.IP "socks5://"
Makes it the equivalent of \-\-socks5
.IP "socks5h://"
Makes it the equivalent of \-\-socks5-hostname
.SH EXIT CODES
There are a bunch of different error codes and their corresponding error
messages that may appear under error conditions. At the time of this writing,
the exit codes are:
.IP 0
Success. The operation completed successfully according to the instructions.
.IP 1
Unsupported protocol. This build of curl has no support for this protocol.
.IP 2
Failed to initialize.
.IP 3
URL malformed. The syntax was not correct.
.IP 4
A feature or option that was needed to perform the desired request was not
enabled or was explicitly disabled at build-time. To make curl able to do
this, you probably need another build of libcurl.
.IP 5
Could not resolve proxy. The given proxy host could not be resolved.
.IP 6
Could not resolve host. The given remote host could not be resolved.
.IP 7
Failed to connect to host.
.IP 8
Weird server reply. The server sent data curl could not parse.
.IP 9
FTP access denied. The server denied login or denied access to the particular
resource or directory you wanted to reach. Most often you tried to change to a
directory that does not exist on the server.
.IP 10
FTP accept failed. While waiting for the server to connect back when an active
FTP session is used, an error code was sent over the control connection or
similar.
.IP 11
FTP weird PASS reply. Curl could not parse the reply sent to the PASS request.
.IP 12
During an active FTP session while waiting for the server to connect back to
curl, the timeout expired.
.IP 13
FTP weird PASV reply, Curl could not parse the reply sent to the PASV request.
.IP 14
FTP weird 227 format. Curl could not parse the 227-line the server sent.
.IP 15
FTP cannot use host. Could not resolve the host IP we got in the 227-line.
.IP 16
HTTP/2 error. A problem was detected in the HTTP2 framing layer. This is
somewhat generic and can be one out of several problems, see the error message
for details.
.IP 17
FTP could not set binary. Could not change transfer method to binary.
.IP 18
Partial file. Only a part of the file was transferred.
.IP 19
FTP could not download/access the given file, the RETR (or similar) command
failed.
.IP 21
FTP quote error. A quote command returned error from the server.
.IP 22
HTTP page not retrieved. The requested URL was not found or returned another
error with the HTTP error code being 400 or above. This return code only
appears if \-\-fail is used.
.IP 23
Write error. Curl could not write data to a local filesystem or similar.
.IP 25
FTP could not STOR file. The server denied the STOR operation, used for FTP
uploading.
.IP 26
Read error. Various reading problems.
.IP 27
Out of memory. A memory allocation request failed.
.IP 28
Operation timeout. The specified time-out period was reached according to the
conditions.
.IP 30
FTP PORT failed. The PORT command failed. Not all FTP servers support the PORT
command, try doing a transfer using PASV instead!
.IP 31
FTP could not use REST. The REST command failed. This command is used for
resumed FTP transfers.
.IP 33
HTTP range error. The range "command" did not work.
.IP 34
HTTP post error. Internal post-request generation error.
.IP 35
SSL connect error. The SSL handshaking failed.
.IP 36
Bad download resume. Could not continue an earlier aborted download.
.IP 37
FILE could not read file. Failed to open the file. Permissions?
.IP 38
LDAP cannot bind. LDAP bind operation failed.
.IP 39
LDAP search failed.
.IP 41
Function not found. A required LDAP function was not found.
.IP 42
Aborted by callback. An application told curl to abort the operation.
.IP 43
Internal error. A function was called with a bad parameter.
.IP 45
Interface error. A specified outgoing interface could not be used.
.IP 47
Too many redirects. When following redirects, curl hit the maximum amount.
.IP 48
Unknown option specified to libcurl. This indicates that you passed a weird
option to curl that was passed on to libcurl and rejected. Read up in the
manual!
.IP 49
Malformed telnet option.
.IP 52
The server did not reply anything, which here is considered an error.
.IP 53
SSL crypto engine not found.
.IP 54
Cannot set SSL crypto engine as default.
.IP 55
Failed sending network data.
.IP 56
Failure in receiving network data.
.IP 58
Problem with the local certificate.
.IP 59
Could not use specified SSL cipher.
.IP 60
Peer certificate cannot be authenticated with known CA certificates.
.IP 61
Unrecognized transfer encoding.
.IP 63
Maximum file size exceeded.
.IP 64
Requested FTP SSL level failed.
.IP 65
Sending the data requires a rewind that failed.
.IP 66
Failed to initialise SSL Engine.
.IP 67
The user name, password, or similar was not accepted and curl failed to log in.
.IP 68
File not found on TFTP server.
.IP 69
Permission problem on TFTP server.
.IP 70
Out of disk space on TFTP server.
.IP 71
Illegal TFTP operation.
.IP 72
Unknown TFTP transfer ID.
.IP 73
File already exists (TFTP).
.IP 74
No such user (TFTP).
.IP 77
Problem reading the SSL CA cert (path? access rights?).
.IP 78
The resource referenced in the URL does not exist.
.IP 79
An unspecified error occurred during the SSH session.
.IP 80
Failed to shut down the SSL connection.
.IP 82
Could not load CRL file, missing or wrong format.
.IP 83
Issuer check failed.
.IP 84
The FTP PRET command failed.
.IP 85
Mismatch of RTSP CSeq numbers.
.IP 86
Mismatch of RTSP Session Identifiers.
.IP 87
Unable to parse FTP file list.
.IP 88
FTP chunk callback reported error.
.IP 89
No connection available, the session will be queued.
.IP 90
SSL public key does not matched pinned public key.
.IP 91
Invalid SSL certificate status.
.IP 92
Stream error in HTTP/2 framing layer.
.IP 93
An API function was called from inside a callback.
.IP 94
An authentication function returned an error.
.IP 95
A problem was detected in the HTTP/3 layer. This is somewhat generic and can
be one out of several problems, see the error message for details.
.IP 96
QUIC connection error. This error may be caused by an SSL library error. QUIC
is the protocol used for HTTP/3 transfers.
.IP XX
More error codes will appear here in future releases. The existing ones
are meant to never change.
.SH BUGS
If you experience any problems with curl, submit an issue in the project's bug
tracker on GitHub: https://github.com/curl/curl/issues
.SH AUTHORS / CONTRIBUTORS
Daniel Stenberg is the main author, but the whole list of contributors is
found in the separate THANKS file.
.SH WWW
https://curl.se
.SH "SEE ALSO"
.BR ftp (1),
.BR wget (1)
