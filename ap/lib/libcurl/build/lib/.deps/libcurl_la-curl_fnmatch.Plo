libcurl_la-curl_fnmatch.lo: ../../curl-7.86.0/lib/curl_fnmatch.c \
 ../../curl-7.86.0/lib/curl_setup.h ../lib/curl_config.h \
 ../../curl-7.86.0/include/curl/system.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/types.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/features.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_config.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/cdefs.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/types.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/wordsize.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include/stddef.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/typesizes.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/pthreadtypes.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/endian.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/endian.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/byteswap.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/byteswap.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/byteswap-common.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/time.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/select.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/select.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/sigset.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/time.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/sysmacros.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/socket.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/uio.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uio.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/socket.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/limits.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed/limits.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/posix1_lim.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/local_lim.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/linux/limits.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_local_lim.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/posix2_lim.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/sockaddr.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm/socket.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm/sockios.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/stdio.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_stdio.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/wchar.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/wchar.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_mutex.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/pthread.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sched.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/sched.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_clk_tck.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/signal.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/setjmp.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_pthread.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include/stdarg.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/stdio_lim.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/assert.h \
 ../../curl-7.86.0/lib/curl_setup_once.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/stdlib.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/alloca.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/string.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/errno.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/errno.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/linux/errno.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm/errno.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm-generic/errno.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm-generic/errno-base.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/stat.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/stat.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/time.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include/stdbool.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/unistd.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/posix_opt.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/confname.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/getopt.h \
 ../../curl-7.86.0/lib/functypes.h ../../curl-7.86.0/lib/curl_ctype.h \
 ../../curl-7.86.0/include/curl/curl.h \
 ../../curl-7.86.0/include/curl/curlver.h \
 ../../curl-7.86.0/include/curl/system.h \
 ../../curl-7.86.0/include/curl/easy.h \
 ../../curl-7.86.0/include/curl/multi.h \
 ../../curl-7.86.0/include/curl/curl.h \
 ../../curl-7.86.0/include/curl/urlapi.h \
 ../../curl-7.86.0/include/curl/options.h \
 ../../curl-7.86.0/include/curl/header.h \
 ../../curl-7.86.0/include/curl/websockets.h \
 ../../curl-7.86.0/include/curl/typecheck-gcc.h \
 ../../curl-7.86.0/lib/curl_fnmatch.h ../../curl-7.86.0/lib/curl_memory.h \
 ../../curl-7.86.0/lib/memdebug.h \
 /home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/fnmatch.h

../../curl-7.86.0/lib/curl_setup.h:

../lib/curl_config.h:

../../curl-7.86.0/include/curl/system.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/types.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/features.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_config.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/cdefs.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/types.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/wordsize.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include/stddef.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/typesizes.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/pthreadtypes.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/endian.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/endian.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/byteswap.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/byteswap.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/byteswap-common.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/time.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/select.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/select.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/sigset.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/time.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/sysmacros.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/socket.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/uio.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uio.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/socket.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/limits.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include-fixed/limits.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/posix1_lim.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/local_lim.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/linux/limits.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_local_lim.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/posix2_lim.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/sockaddr.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm/socket.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm/sockios.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/stdio.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_stdio.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/wchar.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/wchar.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_mutex.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/pthread.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sched.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/sched.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_clk_tck.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/signal.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/setjmp.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/uClibc_pthread.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include/stdarg.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/stdio_lim.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/assert.h:

../../curl-7.86.0/lib/curl_setup_once.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/stdlib.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/alloca.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/string.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/errno.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/errno.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/linux/errno.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm/errno.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm-generic/errno.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/include/asm-generic/errno-base.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/stat.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/stat.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/sys/time.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/build/compiler/gcc-4.9.4_thumb_linux/usr/lib/gcc/arm-buildroot-linux-uclibcgnueabi/4.9.4/include/stdbool.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/unistd.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/posix_opt.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/confname.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/bits/getopt.h:

../../curl-7.86.0/lib/functypes.h:

../../curl-7.86.0/lib/curl_ctype.h:

../../curl-7.86.0/include/curl/curl.h:

../../curl-7.86.0/include/curl/curlver.h:

../../curl-7.86.0/include/curl/system.h:

../../curl-7.86.0/include/curl/easy.h:

../../curl-7.86.0/include/curl/multi.h:

../../curl-7.86.0/include/curl/curl.h:

../../curl-7.86.0/include/curl/urlapi.h:

../../curl-7.86.0/include/curl/options.h:

../../curl-7.86.0/include/curl/header.h:

../../curl-7.86.0/include/curl/websockets.h:

../../curl-7.86.0/include/curl/typecheck-gcc.h:

../../curl-7.86.0/lib/curl_fnmatch.h:

../../curl-7.86.0/lib/curl_memory.h:

../../curl-7.86.0/lib/memdebug.h:

/home/<USER>/work/project-zx297520v3l/origin/Code-origin-p42u28/ap/staging/uClibc/usr/include/fnmatch.h:
