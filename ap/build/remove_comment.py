#!/usr/bin/env python3

import sys

def remove_comment(file_from, file_to, encoding):
    fd = open(file_from, "r", encoding=encoding)
    fd2 = open(file_to, "wb")

    line_no=0
    for line in fd.readlines():
        #line = line.rstrip('\r\n').rstrip('\n')
        line_no += 1
        if line_no == 1:
            fd2.write(line.encode())
            continue
        line_s = line.strip()
        if len(line_s) == 0:
            fd2.write("\n".encode())
            continue
        if line_s[0] == '#':
            #fd2.write("\n".encode())
            continue
        else:
            fd2.write(line.encode())

    fd.close()
    fd2.close()
    return True

if __name__ == '__main__':
    file_from=sys.argv[1]
    file_to=sys.argv[2]
    try:
        remove_comment(file_from, file_to, 'gb18030')
    except:
        try:
            remove_comment(file_from, file_to, 'utf-8')
        except:
            print("[error]remove_comment" + file_from)
            sys.exit(-1)
    