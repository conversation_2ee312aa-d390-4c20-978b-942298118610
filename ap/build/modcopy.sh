#!/bin/bash

BASEDIR=${TOPDIR_AP}
BASE_BAKEUP_DIR=${TOPDIR_AP}/../.tmp/base_backup
LAST_COPY_DIR=${TOPDIR_AP}/../.tmp/lastcopy
LAST_COPY_FILE_LIST=${TOPDIR_AP}/../.tmp/cleanfilelist

function CleanDIR()
{
  echo "===========>rm ${1##*/}<==========="
  rm -fr $1/* 
}

function CopyDIR()
{
  #echo -ne "${1##*../../../}\t\t\t\t\t\t${2##*../../../}"

  if [ ! -d $1 ];then 
   return
  fi
  
  if [ ! -d $2 ];then 
   mkdir -p $2
  fi

  if [[ -d .tmp ]]; then
  	rm -fr ./.tmp/
  fi

  if [[ -d $1 ]]; then
	  if [[ "`ls -A $1 2>/dev/null`" = ".svn" || "`ls -A $1 2>/dev/null`" = "" ]]; then
		  echo -e "$1:\033[0;33;1mnothing done\033[0m"
	  else 
		  mkdir -p .tmp || echo -e "1==>err\n"
		  cp -rp $1/*   ./.tmp/ ||echo -e "2==>err\n"
		  find ./.tmp/ -type d -name '.svn' | xargs rm -fr
		  cp -rp ./.tmp/* $2/ || echo -e "3==>err\n"
#		  cd .tmp
#		  find . -type f -exec echo $2'/'{} \; >> ../.copyfilelist
#		  cd ..
		  rm -fr ./.tmp/
		  echo -e "$1:\033[0;31;1mOK\033[0m"
	  fi
  else
  	echo -e ":\033[0;33;1mNo such directory\033[0m"
  fi
}

function CopyFile()
{
  echo "copy $1 to $2"
  cp -p $1 $2
#  echo $2 >> .copyfilelist
}

function DeleteLastCopy()
{
  cd ${BASEDIR}
  if [[ -f ${LAST_COPY_FILE_LIST} ]]; then
    echo "Delete the last copied files"
    cat ${LAST_COPY_FILE_LIST} | while read v2
    do
      #patchdir=${v2%/*}
      rm  -f "$v2"
    done

    rm -fr ${LAST_COPY_DIR}/*
    rm -f ${LAST_COPY_FILE_LIST}
  else
    echo "first copy,${LAST_COPY_FILE_LIST} NOT EXIST and skip."
  fi
}

function RevertBase()
{
  if [ -d $BASE_BAKEUP_DIR ];then 
    CopyDIR $BASE_BAKEUP_DIR  $BASEDIR
    rm -fr $BASE_BAKEUP_DIR/*
  else
    echo "no $BASE_BAKEUP_DIR  and skip"
  fi
}

function ModeCopy()
{
	###########################1==>copy apps########################################
	#echo "===========>copy files<============="
	CopyDIR  ${PRJ_PRODUCT_DIR}/src ${LAST_COPY_DIR}
}

function BaseBackup()
{
  mkdir -p ${BASE_BAKEUP_DIR}
  cd ${LAST_COPY_DIR} && find ./ -type f > ${LAST_COPY_FILE_LIST}
  cat ${LAST_COPY_FILE_LIST} | while read v2
  do
    patchdir=${v2%/*}
    if [ -f "${BASEDIR}/$v2" ]; then
      mkdir -p ${BASE_BAKEUP_DIR}/$patchdir
      cp "${BASEDIR}/$v2" "${BASE_BAKEUP_DIR}/$v2"
    fi
  done
}
 
echo "[1/6]Check the last copied files"
DeleteLastCopy

echo "[2/6]Revert the base files"
RevertBase

echo "[3/6]copy to last_copy_dir"
ModeCopy

echo "[4/6]Backup base files to backup_dir"
BaseBackup

echo "[5/6]copy files to base_dir from last_copy_dir"
CopyDIR ${LAST_COPY_DIR} ${BASEDIR}

echo "[6/6]${PRJ_NAME} files copy complete"
