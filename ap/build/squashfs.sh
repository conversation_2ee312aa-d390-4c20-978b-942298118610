#!/bin/bash

if [ x"$ERASEBLOCK" = x"" ]; then
  ERASEBLOCK=0x20000
fi

if [ x"$SQUASHFS_BLOCK_KB" = x"" ]; then
  # squashfs 256KB default
  SQUASHFS_BLOCK_KB=256
fi

which mksquashfs > /dev/null 2>&1
if [ $? -eq 0 ]; then
  echo "check command mksquashfs ok"
else
  echo "[error]check command mksquashfs fail, Maybe run [ sudo apt-get install squashfs-tools ] to install"
  exit 1
fi

squashfs_tools_version=`mksquashfs -version | grep "mksquashfs version 4"`
if [ $? -ne 0 ]; then
  echo "[error]Requires the minimum version of squashfs-tools is 4.0"
  echo `mksquashfs -version | grep "mksquashfs version"`
  exit 2
else
  echo "squashfs-tools version check pass"
fi

BLOCK_CNT_SQUASHFS=$(awk "BEGIN { print $SQUASHFS_BLOCK_KB * 1024 / $ERASEBLOCK }")

echo "one squashfs block contain $BLOCK_CNT_SQUASHFS flash block"

mksquashfs $1 $2 -b ${SQUASHFS_BLOCK_KB}k -nopad -noappend -comp xz -Xbcj armthumb -Xdict-size 100% -root-owned -no-xattrs
if [ $? -ne 0 ]; then
  echo "mksquashfs error"
  exit -1
else
  echo "mksquashfs ok"
fi

exit 0
