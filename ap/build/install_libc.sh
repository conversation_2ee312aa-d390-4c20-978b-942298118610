#!/bin/bash

DEST_DIR=$1

#cp uClibc so to /lib
cp -av ${STAGEDIR}/uClibc/lib/* ${DEST_DIR}/lib/

rm -fv ${DEST_DIR}/lib/libubacktrace*

#libgcc_s.so
cp -av ${CROSS_COMPILE_LIBGCC_LIB}* ${DEST_DIR}/lib/
chmod a+x ${DEST_DIR}/lib/libgcc_s.so*

#clean kernel modules
find ${DEST_DIR}/lib/modules -type l -name build  | xargs rm -fr
find ${DEST_DIR}/lib/modules -type l -name source | xargs rm -fr
 
find ${DEST_DIR}/lib/modules/ -name "modules.*" | xargs rm -rfv

if [ x"$USE_LIBSTDCPP" = x"yes" ]; then
  LIBCPP_DIR=`dirname ${CROSS_COMPILE_LIBCPP_LIB}`
  cp -av ${LIBCPP_DIR}/libstdc++.s*[o.0-9]    ${DEST_DIR}/lib/
fi

#strip Executable files and dynamic libraries
SECTIONS_REMOVE="-R .comment -R .note -R .ARM.attributes -R .ARM.exidx -R .ARM.extab"
find ${DEST_DIR}/sbin -type f -perm -u+x -exec ${STRIPTOOL} $SECTIONS_REMOVE '{}' ';'
find ${DEST_DIR}/bin -type f -perm -u+x -exec ${STRIPTOOL} $SECTIONS_REMOVE '{}' ';'
find ${DEST_DIR}/usr/bin -type f -perm -u+x -exec ${STRIPTOOL} $SECTIONS_REMOVE '{}' ';'
chmod a+x ${DEST_DIR}/lib/lib*.so*
find ${DEST_DIR}/lib ! -name \*.ko -type f -perm -u+x -exec ${STRIPTOOL} $SECTIONS_REMOVE '{}' ';'
find ${DEST_DIR}/lib ! -name cpko.ko -name \*.ko -type f -exec ${STRIPTOOL} --strip-debug --strip-unneeded $SECTIONS_REMOVE  '{}' ';'
find ${DEST_DIR}/recovery/bin -type f -perm -u+x -exec ${STRIPTOOL} $SECTIONS_REMOVE '{}' ';'

if [ x"$USE_REMOVE_COMMENT" = x"yes" ]; then
  find ${DEST_DIR} -name "*.sh" -type f -exec ${BUILD_DIR}/remove_comment.sh '{}' ';'
  find ${DEST_DIR} -name "*.conf" -type f -exec sed -i '/^#/d' '{}' ';'
  find ${DEST_DIR} -name "*.cfg"  -type f -exec sed -i '/^#/d' '{}' ';'
  ${BUILD_DIR}/remove_comment.sh ${DEST_DIR}/etc/rc
  sed -i '/^#/d'  ${DEST_DIR}/etc_ro/nvconfig
  find ${DEST_DIR}/etc_ro -name "default_parameter_*"  -type f -exec sed -i '/^#/d' '{}' ';'
fi

