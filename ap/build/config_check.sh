#!/bin/bash

diffconfig=$LINUX_DIR/scripts/diffconfig

config_old=$1
config_new=$2

#diff_cnt=`$diffconfig $config_old $config_new | grep -E  "\-|+"   | grep " y" | wc | awk '{ print $1 }'`
diff_cnt=`$diffconfig $config_old $config_new | grep "-" | grep " y" | wc | awk '{ print $1 }'`
if [ $diff_cnt -gt 0 ]; then
  echo "[error] $config_old -> $config_new difference"
  $diffconfig $config_old $config_new | grep "-" | grep " y"
  exit $diff_cnt
fi

echo "[pass] linux config diffconfig check pass"
exit 0
