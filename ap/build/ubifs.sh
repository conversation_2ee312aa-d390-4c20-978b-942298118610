#!/bin/bash

if [ x"$PAGESIZE" = x"" ]; then
  PAGESIZE=0x800
fi

if [ x"$ERASEBLOCK" = x"" ]; then
  ERASEBLOCK=0x20000
fi

cfg_file_name=$1.cfg
img_file_name=$1.imgbak
vol_size=$[$2*942-512]

rm -fv $img_file_name $cfg_file_name

echo "[ubifs]"                   > $cfg_file_name
echo "mode=ubi"                 >> $cfg_file_name
echo "image=${img_file_name}"   >> $cfg_file_name
echo "vol_id=0"                 >> $cfg_file_name
echo "vol_size=${vol_size}KiB"  >> $cfg_file_name
echo "vol_type=dynamic"         >> $cfg_file_name
echo "vol_name=${1}"     		>> $cfg_file_name
echo "vol_flags=autoresize"     >> $cfg_file_name

mkfs.ubifs -r $3 -m $PAGESIZE -e $[$ERASEBLOCK - $PAGESIZE] -c $[$2*8 -1] -x zlib -o $img_file_name
if [ $? -ne 0 ]; then
  echo "mkfs.ubifs error"
  exit -1
else
  echo "mkfs.ubifs ok"
fi

ubinize -o $4 -m $PAGESIZE -p $ERASEBLOCK -s 512 $cfg_file_name
if [ $? -ne 0 ]; then
  echo "ubinize error"
  exit -2
else
  echo "ubinize ok"
fi

rm -fv $img_file_name $cfg_file_name

exit 0
