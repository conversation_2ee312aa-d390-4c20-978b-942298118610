#!/bin/bash

echo $#

if [ $# -lt 3 ]; then
    echo "$0  app_list_file  rootfs_dir kernel_dir"
    exit 1
fi
APP_LIST_FILE=$1
ROOTFS_DIR=$2
KERNEL_DIR=$3

if [ ! -f $APP_LIST_FILE ]; then
    echo "$APP_LIST_FILE NOT exist"
    exit 2
fi

if [ ! -d $KERNEL_DIR ]; then
    echo "$KERNEL_DIR NOT exist"
    exit 3
fi

if [ ! -d $ROOTFS_DIR ]; then
    echo "$ROOTFS_DIR NOT exist"
    exit 4
fi
VERIFY_APP_HEADER=$KERNEL_DIR/include/linux/verify_app.h

file_array=()
hash_array=()
file_cnt=0
while read file_item; do
    if [ -f ${ROOTFS_DIR}${file_item} ]; then
        hash_val=`sha256sum ${ROOTFS_DIR}${file_item} | awk '{ print $1 }'`
        echo "${file_item} $hash_val ok"
        file_array+=("$file_item")
        hash_array+=("$hash_val")
        let "file_cnt=file_cnt+1"
    else
        echo "regular file ${ROOTFS_DIR}${file_item} NOT exist"
        exit 9
    fi
done < "${APP_LIST_FILE}"

# 打印数组中的数据

echo "#ifndef __VERIFY_APP_H" > $VERIFY_APP_HEADER
echo "#define __VERIFY_APP_H" >> $VERIFY_APP_HEADER
echo ""                       >> $VERIFY_APP_HEADER

echo "const int g_verify_app_cnt = $file_cnt;" >> $VERIFY_APP_HEADER
echo "const char *g_verify_file_array[] = {" >> $VERIFY_APP_HEADER
for element in "${file_array[@]}"; do
    echo "    \"$element\"," >> $VERIFY_APP_HEADER
done
echo "};" >> $VERIFY_APP_HEADER

echo "const char *g_verify_hash_array[] = {" >> $VERIFY_APP_HEADER
for element in "${hash_array[@]}"; do
    echo "    \"$element\"," >> $VERIFY_APP_HEADER
done
echo "};" >> $VERIFY_APP_HEADER

echo ""                       >> $VERIFY_APP_HEADER
echo "#endif" >> $VERIFY_APP_HEADER
