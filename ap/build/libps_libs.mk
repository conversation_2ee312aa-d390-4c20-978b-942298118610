
ifeq ($(DCXO), yes)
LIBPS_PATH=$(LIB_DIR)/libps/$(RF_TYPE)_$(MODEM_TYPE)_dcxo
else
LIBPS_PATH=$(LIB_DIR)/libps/$(RF_TYPE)_$(MODEM_TYPE)
endif

libps_libs =
libps_libs_lto = 
libps_libs_nolto =
ifneq ($(USE_CPPS_KO),yes)
libps_libs_nolto +=  $(LIBPS_PATH)/appstart/appstart.o
endif


libps_libs_nolto += $(LIBPS_PATH)/appstart/appstart.a
libps_libs_nolto +=  $(wildcard $(LIBPS_PATH)/gsml1/*.a)

ifeq ($(USE_ENABLE_LTO),yes)
	libps_libs_lto += $(wildcard $(LIBPS_PATH)/plat/*.o)
	libps_libs_lto += $(wildcard $(LIBPS_PATH)/drv/*.o)
	libps_libs_nolto += $(LIBPS_PATH)/drv/audio_process.a
	libps_libs_lto += $(wildcard $(LIBPS_PATH)/phy/*.o)
	libps_libs_lto += $(wildcard $(LIBPS_PATH)/tools/*.o)
	libps_libs_lto +=  $(wildcard $(LIBPS_PATH)/amt/*.o)
	libps_libs_lto +=  $(wildcard $(LIBPS_PATH)/ref/*.o)
	libps_libs_lto += $(wildcard $(LIBPS_PATH)/ps/*.o)
	libps_libs_lto +=  $(wildcard $(LIBPS_PATH)/psbase/*.o)
	libps_libs_lto +=  $(wildcard $(LIBPS_PATH)/usat/*.o)
else
	libps_libs_nolto += $(wildcard $(LIBPS_PATH)/plat/*.a)
	libps_libs_nolto += $(wildcard $(LIBPS_PATH)/drv/*.a)
	libps_libs_nolto += $(wildcard $(LIBPS_PATH)/phy/*.a)
	libps_libs_nolto += $(wildcard $(LIBPS_PATH)/tools/*.a)
	libps_libs_nolto +=  $(wildcard $(LIBPS_PATH)/amt/*.a)
	libps_libs_nolto +=  $(wildcard $(LIBPS_PATH)/ref/*.a)
	libps_libs_nolto += $(wildcard $(LIBPS_PATH)/ps/*.a)
	libps_libs_nolto +=  $(wildcard $(LIBPS_PATH)/psbase/*.a)
	libps_libs_nolto +=  $(wildcard $(LIBPS_PATH)/usat/*.a)
endif

libps_libs_nolto += $(CROSS_COMPILE_LIB)

libps_libs += $(libps_libs_nolto) 
libps_libs += $(libps_libs_lto)

ifeq ($(USE_ENABLE_LTO),yes)

LTO_CFLAGS = -flto -fno-toplevel-reorder
# $(DISABLE_TL_REORDER)
LTO_FINAL_CFLAGS := -fuse-linker-plugin

# would be needed to support < 5.0
LTO_FINAL_CFLAGS += -fno-toplevel-reorder

LTO_FINAL_CFLAGS += -flto=jobserver

# don't compile everything twice
# requires plugin ar
LTO_CFLAGS += -fno-fat-lto-objects

# Used to disable LTO for specific files (e.g. vdso)
DISABLE_LTO = -fno-lto

# shut up lots of warnings for the compat syscalls
#LTO_CFLAGS += $(call cc-disable-warning,attribute-alias,)

LTO_FINAL_CFLAGS += $(LTO_CFLAGS) -fwhole-program

# most options are passed through implicitely in the LTO
# files per function, but not all.
# should not pass any that may need to be disabled for
# individual files.
#LTO_FINAL_CFLAGS += $(filter -pg,${KBUILD_CFLAGS})
LTO_FINAL_CFLAGS += -fno-strict-aliasing

#ifdef CONFIG_LTO_DEBUG
#LTO_FINAL_CFLAGS += -fdump-ipa-cgraph -fdump-ipa-inline-details
# add for debugging compiler crashes:
# LTO_FINAL_CFLAGS += -dH -save-temps
#endif
#ifdef CONFIG_LTO_CP_CLONE
#LTO_FINAL_CFLAGS += -fipa-cp-clone
#LTO_CFLAGS += -fipa-cp-clone
#endif

else

LTO_CFLAGS =
DISABLE_LTO =
LTO_FINAL_CFLAGS =

endif

CPKO_EXT_CFLAGS =
KERNEL_EXT_CFLAGS =

ifeq ($(USE_TestHarness),yes)
CPKO_EXT_CFLAGS   += -D_USE_TestHarness
endif

ifeq ($(USE_VOICE_SUPPORT),yes)
CPKO_EXT_CFLAGS += -DUSE_VOICE_SUPPORT
endif

ifeq ($(CONFIG_SINGLECORE),yes)
KERNEL_EXT_CFLAGS   += -DCONFIG_SINGLECORE
endif

ifeq ($(CONFIG_MEMCHECK),yes)
KERNEL_EXT_CFLAGS   += -DCONFIG_MEM_CHECK
endif

ifeq ($(CONFIG_KLOCWORK),yes)
KERNEL_EXT_CFLAGS   += -DCONFIG_KLOCWORK
endif

ifeq ($(USE_CAP_SYS),yes)
KERNEL_EXT_CFLAGS   += -D_USE_CAP_SYS
endif
ifeq ($(USE_CAP_SYS),no)
KERNEL_EXT_CFLAGS   += -DDDR_BASE_LEN_CAP=0
KERNEL_EXT_CFLAGS   += -DDDR_BASE_LEN_CAP_BUF=0
endif

KERNEL_EXT_CFLAGS   += -DDDR_BASE_ADDR_LINUX_VA

ifeq ($(USE_BTRUNK_SUPPORT),yes)
KERNEL_EXT_CFLAGS   += -DBTRUNK_SUPPORT
endif

KERNEL_EXT_CFLAGS += $(CPKO_EXT_CFLAGS)

ifeq ($(USE_ZCAT_MBIM),yes)
KERNEL_EXT_CFLAGS   += -D_USE_MBIM
endif

ifeq ($(DCXO),yes)
KERNEL_EXT_CFLAGS   += -DRFC_DCXO_EN
endif

ifeq ($(RF_TYPE),220A1)
KERNEL_EXT_CFLAGS   += -D_USE_RF_ZTE220A1
endif

ifeq ($(RF_TYPE),230A)
KERNEL_EXT_CFLAGS   += -D_USE_RF_ZTE230A
endif

ifeq ($(MODEM_TYPE),lte_only)
KERNEL_EXT_CFLAGS   += -D_USE_LTE_ONLY
endif
