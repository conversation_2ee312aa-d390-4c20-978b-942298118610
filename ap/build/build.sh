#!/bin/bash

#TOPDIR_AP define in project/pubconf.mk
export TOPDIR=${TOPDIR_AP}/..
export CP_ROOT_DIR=${TOPDIR}/cp
export CROSS_ROOT=${TOPDIR}/build/compiler/${CROSS_COMPILE_VERSION}
export CROSS_COMPILE=${CROSS_ROOT}/usr/bin/arm-buildroot-linux-uclibcgnueabi-
export CROSS_COMPILE_LIB=$(${CROSS_COMPILE}gcc -print-libgcc-file-name)
export CROSS_COMPILE_LIBGCC_LIB=$(${CROSS_COMPILE}gcc -print-file-name=libgcc_s.so)
export CROSS_COMPILE_LIBCPP_LIB=$(${CROSS_COMPILE}gcc -print-file-name=libstdc++.so)
export CROSS_COMPILE_INCLUDE_PATH=$(${CROSS_COMPILE}gcc -print-file-name=include)
export CROSS_COMPILE_INCLUDE_FIXED_PATH=$(${CROSS_COMPILE}gcc -print-file-name=include-fixed)
export CROSS_COMPILE_CRTBEGIN=$(${CROSS_COMPILE}gcc -print-file-name=crtbeginT.o)
export CROSS_COMPILE_CRTEND=$(${CROSS_COMPILE}gcc -print-file-name=crtend.o)
export CROSS_COMPILE_SYSROOT=$(${CROSS_COMPILE}gcc -print-sysroot)
export PATH=${CROSS_ROOT}/utils:${CROSS_ROOT}/usr/bin:$PATH
export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:${CROSS_ROOT}/usr/lib
export LC_ALL=C

export CC=${CROSS_COMPILE}gcc
export CXX=${CROSS_COMPILE}g++
export AR=${CROSS_COMPILE}ar
export LD=${CROSS_COMPILE}ld
export STRIPTOOL=${CROSS_COMPILE}strip
export RANLIB=${CROSS_COMPILE}ranlib
export COMMON_BASE_MK=${TOPDIR_AP}/build/base.mk
export COMMON_MK=${TOPDIR_AP}/build/common.mk

export YUICOMPRESSOR=${CROSS_ROOT}/utils/yuicompressor-2.4.8.jar

export OUTPUT_DIR=${TOPDIR_AP}/output
export BUILD_DIR=${TOPDIR_AP}/build
export ROOTFS_DIR=${TOPDIR_AP}/output/rootfs
export RECOVERYFS_DIR=${TOPDIR_AP}/output/recoveryfs
export CAP_ROOTFS_DIR=${TOPDIR_AP}/output/caprootfs
export IMAGEFS_DIR=${TOPDIR_AP}/output/imagefs
export NVROFS_DIR=${TOPDIR_AP}/output/nvrofs
export USERDATA_DIR=${TOPDIR_AP}/output/userdata
export RESOURCE_DIR=${TOPDIR_AP}/output/resource
export OEM_FS_DIR=${TOPDIR_AP}/output/oem
export LINUX_VER=linux-3.4.x
export LOCALVERSION=""
export STAGEDIR=${TOPDIR_AP}/staging
export LIBC_DIR=${TOPDIR_AP}/build/uClibc

export ROMFSINST=${TOPDIR_AP}/build/romfs-inst.sh
export ROMFSDIR=${ROOTFS_DIR}
export IMAGE_DIR=${OUTPUT_DIR}/images

export LINUX_DIR=${TOPDIR_AP}/os/linux/${LINUX_VER}

export PS_BUILD_DIR=${TOPDIR}/cp/ps/project/${CHIP_NAME}/prj_evb/build/linux

export APP_DIR=${TOPDIR_AP}/app
export APP_CFG_MK=${APP_DIR}/config_app.mk
export LIB_DIR=${TOPDIR_AP}/lib
export LIB_CFG_MK=${LIB_DIR}/config_lib.mk
export ZTE_PS_LINK_SCRIPT=${BUILD_DIR}/libps_libs.mk

export BINS_AP_DIR=${TOPDIR}/allbins/${CHIP_NAME}/${PRJ_NAME}/ap

export zte_app_mak=${COMMON_MK}
export zte_lib_mak=${COMMON_MK}
export zte_lib_path=${LIB_DIR}
export zte_app_path=${APP_DIR}

cd ${TOPDIR_AP}; make $*
