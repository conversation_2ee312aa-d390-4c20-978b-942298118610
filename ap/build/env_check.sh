#!/bin/bash

CMD_SET="gawk gcc bison flex pkg-config"
PACKAGE_SET=("gawk" "build-essential" "bison" "flex" "pkg-config")
RET_VALUE=0

function checkCmd()
{
  cmd=$1
  i=$2
  which $cmd > /dev/null 2>&1
  if [ $? -eq 0 ]; then
    echo "check command $cmd ok"
  else
    echo "[ERROR]check command $cmd fail, Maybe run [ sudo apt-get install ${PACKAGE_SET[i]} ] to install"
    return 1
  fi
}

i=0
for item in $CMD_SET;do
  checkCmd $item $i
  if [ $? -ne 0 ]; then
    RET_VALUE=1
  fi
  let "i=i+1"
done

exit $RET_VALUE

