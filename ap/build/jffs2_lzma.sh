#!/bin/bash

if [ x"$PAGESIZE" = x"" ]; then
  PAGESIZE=0x800
fi

if [ x"$ERASEBLOCK" = x"" ]; then
  ERASEBLOCK=0x20000
fi

JFFS2_COMPRESS_TYPE=$3
COMPR_OPT="-X lzma"

if [ x"$JFFS2_COMPRESS_TYPE" = x"zlib" ]; then
  COMPR_OPT="-X zlib"
fi

if [ x"$JFFS2_COMPRESS_TYPE" = x"lzo" ]; then
  COMPR_OPT="-X lzo"
fi

mkfs.jffs2_lzma -v $COMPR_OPT --pagesize=$PAGESIZE --eraseblock=$ERASEBLOCK --little-endian --no-cleanmarkers --squash -d $1 -o $2
if [ $? -ne 0 ]; then
  echo "mkfs.jffs2_lzma error"
  exit -1
else
  echo "mkfs.jffs2_lzma ok"
fi

exit 0
