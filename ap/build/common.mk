
include $(COMMON_BASE_MK)

ifeq ($(USERDATA_FS_TYPE),jffs2)
CFLAGS   += -DUSE_JFFS2
endif

ifeq ($(USERDATA_FS_TYPE),ubifs)
CFLAGS   += -DUSE_UBIFS
endif

ifeq ($(GLOBAL_DEBUG),yes)
CFLAGS   += -DGLOBAL_DEBUG
endif

ifneq ($(CUSTOM_MACRO),"")
CFLAGS   += $(CUSTOM_MACRO)
endif

ifeq ($(CONFIG_SINGLECORE),yes)
CFLAGS   += -DCONFIG_SINGLECORE
endif

ifeq ($(USE_FOTA_AB),yes)
CFLAGS   += -DFOTA_AB
endif
