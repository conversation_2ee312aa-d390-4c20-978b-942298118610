
#include linux kernel .config file
-include $(LINUX_DIR)/.config

#include lib and app config file
include $(LIB_CFG_MK)
include $(APP_CFG_MK)

CFLAGS := -g -Os -pipe -fno-builtin -Wall

CFLAGS += -ffunction-sections

CFLAGS += -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums

ifeq ($(THUMB),yes)
CFLAGS += -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb
endif

CFLAGS += -nostdinc 
CFLAGS += -idirafter $(STAGEDIR)/include
CFLAGS += -isystem   $(STAGEDIR)/uClibc/usr/include
CFLAGS += -isystem   $(CROSS_COMPILE_INCLUDE_FIXED_PATH)
CFLAGS += -isystem   $(CROSS_COMPILE_INCLUDE_PATH)

LDFLAGS := -g -fno-common -fno-builtin

LDFLAGS += -Wl,--gc-sections

TARGETARCH := arm-linux
HOSTARCH := $(shell sh $(BUILD_DIR)/config.guess)

CONFIGURE_OPTS = --host=$(TARGETARCH) --build=$(HOSTARCH) --target=$(TARGETARCH)

ARFLAGS := rcs

export CFLAGS
export LDFLAGS
export ARFLAGS

#LDFLAGS -static
#$(CC) $(LD_BEGIN) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) $(LDLIBS_$@) -Wl,--end-group $(LD_END)
LD_BEGIN = -nostdlib
LD_BEGIN += $(STAGEDIR)/uClibc/usr/lib/crt1.o
LD_BEGIN += $(STAGEDIR)/uClibc/usr/lib/crti.o
LD_BEGIN += $(CROSS_COMPILE_CRTBEGIN)

LD_END = -L $(STAGEDIR)/uClibc/lib
LD_END += -Wl,-rpath-link,$(STAGEDIR)/uClibc/lib
LD_END += -L $(STAGEDIR)/uClibc/usr/lib
LD_END += -Wl,-rpath-link,$(STAGEDIR)/uClibc/usr/lib
LD_END += -Wl,--start-group -lgcc -lgcc_eh -lc -Wl,--end-group
LD_END += $(CROSS_COMPILE_CRTEND)
LD_END += $(STAGEDIR)/uClibc/usr/lib/crtn.o

